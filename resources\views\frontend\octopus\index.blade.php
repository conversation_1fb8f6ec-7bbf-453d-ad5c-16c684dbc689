@extends('frontend.octopus.layouts.master')

@section('content')
    <!-- start banner Area -->
    <section class="banner-area relative" id="home">
        <div class="overlay overlay-bg"></div>
        <div class="container">
            <div id="bnr1" class="row fullscreen d-flex align-items-center justify-content-center" style="height: 250px; !important;">
                <div class="banner-content col-lg-12">

                    <form action="{{route('jobs.search')}}" method="GET" class="search-form-area" id="jobSearchForm">
                        <div class="row justify-content-center form-wrap">
                            <div class="col-lg-6 form-cols">
                                <input type="text" class="form-control" name="keywords" placeholder="Enter Keywords, Skills, Designation" value="{{ request('keywords') }}">
                            </div>

                             <div class="col-lg-4 form-cols">
                                <input type="text" class="form-control" name="location" placeholder="Enter Location" value="{{ request('location') }}">
                            </div>

                            <div class="col-lg-2 form-cols">
                                <button type="submit" class="btn btn-info" style="color:white">
                                    <span class="lnr lnr-magnifier"> Search </span>
                                </button>
                                </br>
                                <a id="sMore" href="#"> Search options </a>
                            </div>
                        </div>

                        <!-- Hidden inputs for advanced search -->
                        <input type="hidden" name="job_type" id="job_type_input" value="{{ request('job_type') }}">
                        <input type="hidden" name="remote_option" id="remote_option_input" value="{{ request('remote_option') }}">

                        <div id="panelSMore" style="border-top: 0px solid #b8aaf3; display: none;" class="row justify-content-center form-wrap">
                            <div class="col-lg-12 form-cols">
                                <ul class="nav nav-tabs  row justify-content-center">
                                    <li id="list" class="active"><a data-toggle="tab" href="#menu1">Employment Types</a></li>
                                    <li id="list"><a data-toggle="tab" href="#menu2">Salary - Remuneration</a></li>
                                    <li id="list"><a data-toggle="tab" href="#menu3">Sort by Time</a></li>
                                    <li id="list"><a data-toggle="tab" href="#menu4">Work Location Type</a></li>
                                </ul>
                                <div class="tab-content">
                                    <div id="menu1" class="tab-pane fade in active">
                                        <div class="btn-group nav-tabs">
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="">All</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="full_time">Full-time</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="part_time">Part-time</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="casual">Casual</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="contract">Contract</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="internship">Internship</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="online">Online</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="freelance">Freelance</button>
                                        </div>
                                    </div>
                                    <div id="menu2" class="tab-pane fade">
                                        <div class="row justify-content-center ">
                                            <div class="col-lg-4">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary">
                                                         <select name="salary_min" class="form-control">
                                                            <option value="">Minimum Salary</option>
                                                            <option value="0" {{ request('salary_min') == '0' ? 'selected' : '' }}>Any</option>
                                                            <option value="50000" {{ request('salary_min') == '50000' ? 'selected' : '' }}>₹50,000</option>
                                                            <option value="100000" {{ request('salary_min') == '100000' ? 'selected' : '' }}>₹1,00,000</option>
                                                            <option value="200000" {{ request('salary_min') == '200000' ? 'selected' : '' }}>₹2,00,000</option>
                                                            <option value="300000" {{ request('salary_min') == '300000' ? 'selected' : '' }}>₹3,00,000</option>
                                                            <option value="500000" {{ request('salary_min') == '500000' ? 'selected' : '' }}>₹5,00,000</option>
                                                            <option value="700000" {{ request('salary_min') == '700000' ? 'selected' : '' }}>₹7,00,000</option>
                                                            <option value="1000000" {{ request('salary_min') == '1000000' ? 'selected' : '' }}>₹10,00,000</option>
                                                            <option value="1500000" {{ request('salary_min') == '1500000' ? 'selected' : '' }}>₹15,00,000+</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary">
                                                        <select name="salary_max" class="form-control">
                                                            <option value="">Maximum Salary</option>
                                                            <option value="100000" {{ request('salary_max') == '100000' ? 'selected' : '' }}>₹1,00,000</option>
                                                            <option value="200000" {{ request('salary_max') == '200000' ? 'selected' : '' }}>₹2,00,000</option>
                                                            <option value="300000" {{ request('salary_max') == '300000' ? 'selected' : '' }}>₹3,00,000</option>
                                                            <option value="500000" {{ request('salary_max') == '500000' ? 'selected' : '' }}>₹5,00,000</option>
                                                            <option value="700000" {{ request('salary_max') == '700000' ? 'selected' : '' }}>₹7,00,000</option>
                                                            <option value="1000000" {{ request('salary_max') == '1000000' ? 'selected' : '' }}>₹10,00,000</option>
                                                            <option value="1500000" {{ request('salary_max') == '1500000' ? 'selected' : '' }}>₹15,00,000</option>
                                                            <option value="2000000" {{ request('salary_max') == '2000000' ? 'selected' : '' }}>₹20,00,000+</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="menu3" class="tab-pane fade">
                                        <div class="row justify-content-center ">
                                            <div class="col-lg-4" id="meeting1">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary" id="meeting" >
                                                         <select name="date_posted" class="form-control" style="height: 40px; width: 160px;">
                                                            <option value="">All Time</option>
                                                            <option value="1" {{ request('date_posted') == '1' ? 'selected' : '' }}>Last 24 hours</option>
                                                            <option value="7" {{ request('date_posted') == '7' ? 'selected' : '' }}>Last 7 days</option>
                                                            <option value="30" {{ request('date_posted') == '30' ? 'selected' : '' }}>Last 30 days</option>
                                                            <option value="90" {{ request('date_posted') == '90' ? 'selected' : '' }}>Last 3 months</option>
                                                            <option value="180" {{ request('date_posted') == '180' ? 'selected' : '' }}>Last 6 months</option>
                                                        </select>
                                                        <!-- <a style="font-size:20px; color:white;">OR </a> -->
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4" id="meeting2">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary" >
                                                        <label for="date_from" style="color:white; margin-right: 10px">From:</label>
                                                        <input name="date_from" type="date" class="form-control" value="{{ request('date_from') }}"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4" id="meeting3">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary" >
                                                        <label for="date_to" style="color:white; margin-right: 10px">To:</label>
                                                        <input name="date_to" type="date" class="form-control" value="{{ request('date_to') }}"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="menu4" class="tab-pane fade">
                                        <div>
                                            <button type="button" class="btn btn-primary remote-btn" data-remote="">All</button>
                                            <button type="button" class="btn btn-primary remote-btn" data-remote="0">OnSite</button>
                                            <button type="button" class="btn btn-primary remote-btn" data-remote="1">Remote</button>
                                            <button type="button" class="btn btn-primary remote-btn" data-remote="hybrid">Hybrid</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
           </div>
        </div>
    </section>
    <!-- End banner Area -->
    
     <!-- Start feature-cat Area -->
    <section class="feature-cat-area pt-10" id="category">
        <div class="container">
            <div class="row d-flex justify-content-center">
                <div class="menu-content pb-10 col-lg-10" >
                    <div class="title text-center">
                        <h4 class="mb-10">Featured Job Categories</h4>
                        
                    </div>
                </div>
            </div>
            <div class="row">
                @if(isset($featuredCategories) && $featuredCategories->count() > 0)
                    @foreach($featuredCategories as $category)
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <div class="single-fcat">
                                <a href="{{ route('category.jobs', $category->slug) }}">
                                    <img id="imgfcat" src="{{url('theme/web/img/o' . (($loop->index % 6) + 1) . '.png')}}" alt="{{ $category->name }}">
                                </a>
                                <p><a href="{{ route('category.jobs', $category->slug) }}">{{ $category->name }}</a></p>
                                @if($category->job_listings_count > 0)
                                    <small class="text-muted">({{ $category->job_listings_count }} jobs)</small>
                                @endif
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="col-12 text-center">
                        <p class="text-muted">No categories available at the moment.</p>
                    </div>
                @endif
            </div>
        </div>
    </section>
    <!-- end feature-cat Area -->

    
    
       <!-- Start post Area -->
   <section class="post-area section-gap">
        <div class="container">
            <div class="row justify-content-center d-flex">
                <div class="col-lg-3 sidebar">                                            
                    <div class="single-slidebar" id="moresidebar">
                        <h4 style="text-align:center;margin-bottom: 10px;">Top Recruiters</h4>
                        @if(isset($topRecruiters) && $topRecruiters->count() > 0)
                            @foreach($topRecruiters as $recruiter)
                                <div class="recruiter-item mb-2">
                                    @if($recruiter->logo)
                                        <img class="mimage" src="{{asset('storage/' . $recruiter->logo)}}" alt="{{ $recruiter->name }}">
                                    @else
                                        <div class="company-placeholder">{{ substr($recruiter->name, 0, 2) }}</div>
                                    @endif
                                    <p class="company-name">{{ $recruiter->name }}</p>
                                    <small class="text-muted">{{ $recruiter->job_listings_count }} jobs</small>
                                </div>
                            @endforeach
                        @else
                            <p class="text-muted text-center">No recruiters available.</p>
                        @endif
                    </div>


                    <div class="single-slidebar" id="moresidebar">
                        <h4 style="text-align:center;margin-bottom: 10px;">Jobs by Location</h4>
                        @if(isset($jobsByLocation) && $jobsByLocation->count() > 0)
                            <ul class="list-unstyled">
                                @foreach($jobsByLocation as $location)
                                    <li class="mb-2">
                                        <a href="{{ route('jobs.search', ['location' => $location->location]) }}" class="text-decoration-none">
                                            <i class="fa fa-map-marker"></i> {{ $location->location }}
                                            <span class="badge badge-primary float-right">{{ $location->count }}</span>
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <p class="text-muted text-center">No location data available.</p>
                        @endif
                    </div>







                </div>

                <!--Job ad display section-->

                <div class="col-lg-6 post-list">
                    <h4 class="mb-4">Latest Jobs</h4>

                    @if(isset($recentJobs) && $recentJobs->count() > 0)
                        @foreach($recentJobs as $job)
                            <div class="single-post d-flex flex-row mb-4">
                                <div class="details col-lg-12 col-md-12 col-sm-12">
                                    <div class="title d-flex flex-row justify-content-between">
                                        <div class="titles">
                                            <a href="{{ route('frontend.job.show', $job->slug) }}">
                                                <h3 style="margin-top:10px;" class="main-color">{{ $job->title }}</h3>
                                            </a>
                                            <h6><b>{{ $job->company ? $job->company->name : 'Company Name' }}</b></h6>
                                        </div>
                                    </div>
                                    <div class="title justify-content-between">
                                        <div class="titles mb-15">
                                            <a id="locate1"><b>Exp:</b> {{ $job->experience_level ?? 'Not specified' }}</a>
                                            <a><span class="fa fa-map-marker locate"></span> {{ $job->location }}</a>
                                            @if($job->salary_min && $job->salary_max)
                                                <a><b>Salary:</b> ₹{{ number_format($job->salary_min) }} - ₹{{ number_format($job->salary_max) }}</a>
                                            @endif
                                        </div>
                                        @if($job->skills_required)
                                            <h5 style="text-align:justify;"><b>KeySkills:</b><span style="line-height: 1.9;">
                                                {{ is_array($job->skills_required) ? implode(', ', array_slice($job->skills_required, 0, 5)) : $job->skills_required }}
                                                @if(is_array($job->skills_required) && count($job->skills_required) > 5)...@endif
                                            </span></h5>
                                        @endif
                                        <h5 style="text-align:justify;"><b>Description:</b><span style="line-height: 1.9;">
                                            {{ Str::limit(strip_tags($job->description), 100) }}
                                        </span>
                                        <a href="{{ route('frontend.job.show', $job->slug) }}" class="main-color">More...</a></h5>
                                    </div>
                                    <ul class="btns">
                                        <li class="ml-5 float-right "><a href="{{ route('frontend.job.show', $job->slug) }}">Apply</a></li>
                                        <li class="ml-5 float-right ">
                                            <a href="#" onclick="toggleShortlist({{ $job->id }})"
                                               style="color: {{ isset($job->is_shortlisted) && $job->is_shortlisted ? '#28a745' : '#6c757d' }}">
                                                {{ isset($job->is_shortlisted) && $job->is_shortlisted ? 'Shortlisted' : 'Shortlist' }}
                                            </a>
                                        </li>
                                        <li id="view">
                                            <a href="#" onclick="toggleFavorite({{ $job->id }})">
                                                <i style="color:#ff0066;" class="fa {{ isset($job->is_favorited) && $job->is_favorited ? 'fa-heart' : 'fa-heart-o' }} fa-lg"></i>
                                            </a>
                                        </li>
                                        <li id="view"><a style="color:black;"><i style="color:black;" class="fa fa-eye fa-lg"></i> {{ $job->views_count ?? 0 }}</a></li>
                                    </ul>
                                </div>
                                <div class="none">
                                    <ul>
                                        <li>
                                            @if($job->company && $job->company->logo)
                                                <img class="indeximage" src="{{ asset('storage/' . $job->company->logo) }}" alt="{{ $job->company->name }}">
                                            @else
                                                <img class="indeximage" src="{{url('theme/web/img/company.jpg')}}" alt="Company">
                                            @endif
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        @endforeach

                        <div class="text-center mt-4">
                            <a class="text-uppercase loadmore-btn mx-auto d-block loadmore" href="{{ route('jobs.all') }}">View All Jobs</a>
                        </div>
                    @else
                        <div class="text-center">
                            <p class="text-muted">No jobs available at the moment.</p>
                            <a href="{{ route('jobs.all') }}" class="btn btn-primary">Browse All Jobs</a>
                        </div>
                    @endif
                </div>

                <div class="col-lg-3 sidebar">
                    <div class="single-slidebar">
                        <h4>Jobs by Location</h4>
                        @if(isset($jobsByLocation) && $jobsByLocation->count() > 0)
                            <ul class="cat-list">
                                @foreach($jobsByLocation as $location)
                                    <li style="margin: 0px;">
                                        <a class="justify-content-between d-flex" href="{{ route('jobs.search', ['location' => $location->location]) }}">
                                            <p>{{ $location->location }}</p>
                                            <span>{{ number_format($location->count) }}</span>
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <p class="text-muted text-center">No location data available.</p>
                        @endif
                    </div>
                    <div class="single-slidebar">
                        <h4>Featured Jobs</h4>
                        @if(isset($recentJobs) && $recentJobs->count() > 0)
                            <div class="active-relatedjob-carusel">
                                @foreach($recentJobs->take(2) as $featuredJob)
                                    <div class="single-rated mb-4">
                                        @if($featuredJob->company && $featuredJob->company->logo)
                                            <img class="img-fluid" src="{{ asset('storage/' . $featuredJob->company->logo) }}" alt="{{ $featuredJob->company->name }}">
                                        @else
                                            <img class="img-fluid" src="{{url('theme/web/img/r1.jpg')}}" alt="Company">
                                        @endif
                                        <a href="{{ route('frontend.job.show', $featuredJob->slug) }}"><h4>{{ $featuredJob->title }}</h4></a>
                                        <h6>{{ $featuredJob->company ? $featuredJob->company->name : 'Company Name' }}</h6>
                                        <p>{{ Str::limit(strip_tags($featuredJob->description), 100) }}</p>
                                        <h5>Job Nature: {{ ucfirst(str_replace('_', ' ', $featuredJob->job_type)) }}</h5>
                                        <p class="address"><span class="lnr lnr-map"></span> {{ $featuredJob->location }}</p>
                                        @if($featuredJob->salary_min && $featuredJob->salary_max)
                                            <p class="address"><span class="lnr lnr-database"></span> ₹{{ number_format($featuredJob->salary_min) }} - ₹{{ number_format($featuredJob->salary_max) }}</p>
                                        @endif
                                        <a href="{{ route('frontend.job.show', $featuredJob->slug) }}" class="btns text-uppercase">Apply job</a>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-muted text-center">No featured jobs available.</p>
                        @endif
                    </div>

                </div>
            </div>
        </div>
    </section>
    <!-- End post Area -->
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#sMore').click(function(e){
        e.preventDefault();
        if($('#panelSMore').is(":visible")){
            $('#panelSMore').slideUp(800);
        } else {
            $('#panelSMore').slideDown(800);
        }
    });

    // Set active states based on current request values
    var currentJobType = '{{ request("job_type") }}';
    var currentRemoteOption = '{{ request("remote_option") }}';

    if(currentJobType) {
        $('.job-type-btn[data-type="' + currentJobType + '"]').addClass('active');
    } else {
        $('.job-type-btn[data-type=""]').addClass('active');
    }

    if(currentRemoteOption !== '') {
        $('.remote-btn[data-remote="' + currentRemoteOption + '"]').addClass('active');
    } else {
        $('.remote-btn[data-remote=""]').addClass('active');
    }

    // Job type filter functionality
    $('.job-type-btn').click(function() {
        $('.job-type-btn').removeClass('active');
        $(this).addClass('active');
        $('#job_type_input').val($(this).data('type'));
    });

    // Remote option filter functionality
    $('.remote-btn').click(function() {
        $('.remote-btn').removeClass('active');
        $(this).addClass('active');
        $('#remote_option_input').val($(this).data('remote'));
    });
});

// Shortlist functionality
function toggleShortlist(jobId) {
    @auth
        // Add AJAX call to handle shortlisting
        $.ajax({
            url: '/jobs/' + jobId + '/shortlist',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if(response.success) {
                    // Toggle shortlist button text and color
                    var shortlistLink = $('a[onclick="toggleShortlist(' + jobId + ')"]');
                    if(response.shortlisted) {
                        shortlistLink.text('Shortlisted').css('color', '#28a745');
                    } else {
                        shortlistLink.text('Shortlist').css('color', '#6c757d');
                    }
                    // Show success message
                    alert(response.message);
                }
            },
            error: function(xhr) {
                if(xhr.status === 401) {
                    alert('Please login to shortlist jobs.');
                    window.location.href = '{{ route("login") }}';
                } else {
                    alert('Please try again later.');
                }
            }
        });
    @else
        alert('Please login to shortlist jobs.');
        window.location.href = '{{ route("login") }}';
    @endauth
}

// Favorite functionality
function toggleFavorite(jobId) {
    @auth
        // Add AJAX call to handle favorites
        $.ajax({
            url: '/jobs/' + jobId + '/favorite',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if(response.success) {
                    // Toggle heart icon
                    var heartIcon = $('a[onclick="toggleFavorite(' + jobId + ')"] i');
                    if(response.favorited) {
                        heartIcon.removeClass('fa-heart-o').addClass('fa-heart');
                    } else {
                        heartIcon.removeClass('fa-heart').addClass('fa-heart-o');
                    }
                    // Show success message
                    alert(response.message);
                }
            },
            error: function(xhr) {
                if(xhr.status === 401) {
                    alert('Please login to favorite jobs.');
                    window.location.href = '{{ route("login") }}';
                } else {
                    alert('Please try again later.');
                }
            }
        });
    @else
        alert('Please login to favorite jobs.');
        window.location.href = '{{ route("login") }}';
    @endauth
}
</script>
@endpush
