

<!doctype html>
<html lang="en">
  <head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet"/> -->

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
   
     <!-- calender -->
     <script type="text/javascript" src="https://code.jquery.com/jquery-1.11.3.min.js"></script>

    <!-- Isolated Version of Bootstrap, not needed if your site already uses Bootstrap -->
    <link rel="stylesheet" href="https://formden.com/static/cdn/bootstrap-iso.css" />

    <!-- Bootstrap Date-Picker Plugin -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.4.1/js/bootstrap-datepicker.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.4.1/css/bootstrap-datepicker3.css"/>
    
    <!-- calender -->
    
<style>
    .jumbotron {
    
    background-color:#9400D3; 
    height:10px;
    padding-top:10px;
    padding-bottom:30px;
    color: #ffffff;
    font-size: 16px;
    }

    .container {

        margin-top:-15px;
        margin-bottom:10px;
        margin-left:20%px;
        margin-right:20%px;

    }

    h5:hover {
    text-decoration: underline;
    text-decoration: underline blue;
    }

    input[type="radio"]{
    margin: 0 15px 0 10px;
    }

    .formHeading1{
    margin-top:-25px;
    font:  bold 15px Arial, serif;
    margin-bottom:15px;
    }


    .bg-img {
    /* The image used */
        background-image: url("theme/web/img/skyform.jpg");

    min-height: 450px;

    /* Center and scale the image nicely */
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    }

    
    .register{
        background: -webkit-linear-gradient(right, #4B0082, #cc99ff);
        margin-top: 3%;
        padding: 3%;
    }

    .register-left{
        text-align: center;
        color: #fff;
        margin-top: 4%;
    }

    .register-left input{
        border: none;
        border-radius: 1.5rem;
        padding: 2%;
        width: 60%;
        background: #f8f9fa;
        font-weight: bold;
        color: #383d41;
        margin-top: 10%;
        margin-bottom: 3%;
        cursor: pointer;
    }

    .register-right{
        background: #dedede;
        border-top-right-radius: 10% 50%;
        border-bottom-right-radius: 10% 50%;
    }

    .register-left img{
        margin-top: 15%;
        margin-bottom: 5%;
        width: 25%;
        -webkit-animation: mover 2s infinite  alternate;
        animation: mover 1s infinite  alternate;
    }

    @-webkit-keyframes mover {
        0% { transform: translateY(0); }
        100% { transform: translateY(-20px); }
    }

    @keyframes mover {
        0% { transform: translateY(0); }
        100% { transform: translateY(-20px); }
    }

    .register-left p{
        font-weight: lighter;
        padding: 12%;
        margin-top: -9%;
    }

    .register .register-form{
        padding: 10%;
        margin-top: 10%;
    }

    .btnRegister{
        float: right;
        margin-top: 10%;
        border: none;
        border-radius: 1.5rem;
        padding: 2%;
        background: #9400D3;
        color: #fff;
        font-weight: 600;
        width: 50%;
        cursor: pointer;
    }

    .register .nav-tabs{
        margin-top: 3%;
        border: none;
        background: #0062cc;
        border-radius: 1.5rem;
        width: 28%;
        float: right;
    }

    .register .nav-tabs .nav-link{
        padding: 2%;
        height: 34px;
        font-weight: 600;
        color: #fff;
        border-top-right-radius: 1.5rem;
        border-bottom-right-radius: 1.5rem;
    }

    .register .nav-tabs .nav-link:hover{
        border: none;
    }

    .register .nav-tabs .nav-link.active{
        width: 100px;
        color: #0062cc;
        border: 2px solid #0062cc;
        border-top-left-radius: 1.5rem;
        border-bottom-left-radius: 1.5rem;
    }

    .register-heading{
        text-align: center;
        margin-top: 8%;
        margin-bottom: -15%;
        color: #495057;
    }

</style>
     
    <title>Employer  Registration</title> 
</head> 

<body class="container">

<a href="{{url('')}}">
    <img src="{{url('theme/web/img/logo.png')}}" alt="jobs" width="270" height="60"
    style="margin-top:20px"></a>

<div class="jumbotron text-center">
     <h5> Register on Job Octopus. Be in reach of million candidates !</h5>
</div>

<div> 
<!--<p class="formHeading1">Fill the details below for quick registartion</p> </div>-->
 <hr>


    <div class="container register">
        <div class="row">            
            <div class="col-md-9 register-right">
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                        <h3 class="register-heading">Fill the details below for quick registartion</h3>
                        <div class="row register-form">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Company Name (Business, Trade, Entity name) *" value="" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="First Name *" value="" />
                                </div>
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Land Line Number"  />
                                </div>

                                  <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Enter Your Email Id *"  />
                                </div>  
                                <div class="form-group">
                                    <input type="password" class="form-control" placeholder="Password *" value="" />
                                </div>

                                <div class="form-group">
                                    <select class="form-control">
                                        <option class="hidden"  selected disabled>Designation</option>
                                        <option>IT</option>
                                        <option> Manager </option>
                                        <option>Executive</option>
                                        <option>Technical </option>
                                        <option>Others</option>
                                    </select>
                                </div>  	

       	                         
                                       
                                <div class="form-group">
                                <br>
                                    <div class="maxl">
                                        <label class="radio inline"> 
                                            <input type="checkbox" name="gender" value="male" checked>
                                            <span> I agree to the terms & conditions. </span> 
                                        </label>                                        
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="email" class="form-control" placeholder="Last Name *" value="" />
                                </div>
                                <div class="form-group">
                                    <input type="text" minlength="10" maxlength="10" name="txtEmpPhone" class="form-control" placeholder="Mobile Number *" value="" />
                                </div>

                                 <div class="form-group">
                                    <input type="email" class="form-control" placeholder="Enter Location *" value="" />
                                </div>
                                 <div class="form-group">
                                    <input type="password" class="form-control"  placeholder="Confirm Password *" value="" />
                                </div>                                                                   
                                                              
                                <input type="submit" class="btnRegister"  value="Register"/>
                            </div>
                        </div>
                    </div>
               </div>
            </div>
            <div class="col-md-3 register-left">
                <img src="https://image.ibb.co/n7oTvU/logo_white.png" alt=""/>
                <h3>Welcome</h3>
                <p>Already a Member</p>
                <input type="submit" name="" value="Post Job Ad"/><br/>
                <p>OR</p>
                <input type="submit" name="" value="Login"/><br/>
            </div>
        </div>
    </div>
</br>
</body>
</html> 