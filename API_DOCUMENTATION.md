# JobOctopus API Documentation

## Overview
This document provides information about the JobOctopus job portal API endpoints for frontend-backend connectivity.

## Base URL
```
http://your-domain.com
```

## Authentication
Most endpoints require user authentication. Users must be logged in to access protected endpoints.

## CSRF Protection
All POST, PUT, PATCH, DELETE requests require CSRF token. Include the token in:
- Header: `X-CSRF-TOKEN`
- Form field: `_token`

## Rate Limiting
- General endpoints: 60 requests per minute
- Job interaction endpoints: 30 requests per minute

---

## Job Interaction Endpoints

### Toggle Job Favorite
**POST** `/jobs/{id}/favorite`

Toggle favorite status for a job listing.

**Parameters:**
- `id` (required): Job listing ID

**Headers:**
```
Content-Type: application/json
X-CSRF-TOKEN: {csrf_token}
```

**Response:**
```json
{
    "success": true,
    "favorited": true,
    "message": "Job added to favorites."
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Please login to favorite jobs."
}
```

### Toggle Job Shortlist
**POST** `/jobs/{id}/shortlist`

Toggle shortlist status for a job listing.

**Parameters:**
- `id` (required): Job listing ID

**Request Body:**
```json
{
    "notes": "Optional notes about this job"
}
```

**Response:**
```json
{
    "success": true,
    "shortlisted": true,
    "message": "Job added to shortlist."
}
```

---

## Job Search Endpoints

### Search Jobs
**GET** `/search-jobs`

Search and filter job listings.

**Query Parameters:**
- `keywords` (optional): Search keywords
- `location` (optional): Job location
- `category` (optional): Category ID
- `job_type` (optional): full_time, part_time, contract, freelance, internship, volunteering, casual, business, franchise, online
- `salary_min` (optional): Minimum salary
- `salary_max` (optional): Maximum salary
- `experience_level` (optional): entry, mid, senior, executive
- `remote_option` (optional): 0, 1, hybrid
- `date_posted` (optional): 1, 7, 30, 90, 180 (days)
- `date_from` (optional): Date from (YYYY-MM-DD)
- `date_to` (optional): Date to (YYYY-MM-DD)
- `sort_by` (optional): latest, salary_high, salary_low, oldest

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "title": "Software Developer",
            "slug": "software-developer",
            "description": "Job description...",
            "location": "New York",
            "job_type": "full_time",
            "salary_min": 50000,
            "salary_max": 80000,
            "experience_level": "mid",
            "remote_option": true,
            "views_count": 150,
            "created_at": "2025-07-30T10:00:00.000000Z",
            "company": {
                "id": 1,
                "name": "Tech Company",
                "logo": "logo.png",
                "slug": "tech-company"
            },
            "categories": [
                {
                    "id": 1,
                    "name": "Technology",
                    "slug": "technology"
                }
            ],
            "is_favorited": false,
            "is_shortlisted": false
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 5,
        "per_page": 12,
        "total": 50
    }
}
```

---

## User Favorites & Shortlists

### Get User Favorites
**GET** `/user/favorites`

Get user's favorite jobs.

**Response:**
```json
{
    "success": true,
    "favorites": [
        {
            "id": 1,
            "job_listing": {
                "id": 1,
                "title": "Software Developer",
                "company": {
                    "name": "Tech Company"
                }
            },
            "created_at": "2025-07-30T10:00:00.000000Z"
        }
    ]
}
```

### Get User Shortlists
**GET** `/user/shortlists`

Get user's shortlisted jobs.

**Response:**
```json
{
    "success": true,
    "shortlists": [
        {
            "id": 1,
            "notes": "Interesting position",
            "job_listing": {
                "id": 1,
                "title": "Software Developer",
                "company": {
                    "name": "Tech Company"
                }
            },
            "created_at": "2025-07-30T10:00:00.000000Z"
        }
    ]
}
```

---

## Error Codes

- `200` - Success
- `400` - Bad Request (Invalid input)
- `401` - Unauthorized (Login required)
- `403` - Forbidden (Access denied)
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests (Rate limit exceeded)
- `500` - Internal Server Error

---

## Testing Instructions

### 1. Setup
1. Ensure the application is running
2. Create a user account or use existing credentials
3. Obtain CSRF token from the page meta tag or login form

### 2. Testing Favorites
```javascript
// Get CSRF token
const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

// Toggle favorite
fetch('/jobs/1/favorite', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': token
    }
})
.then(response => response.json())
.then(data => console.log(data));
```

### 3. Testing Search
```javascript
// Search jobs
fetch('/search-jobs?keywords=developer&location=new+york&job_type=full_time')
.then(response => response.json())
.then(data => console.log(data));
```

### 4. Frontend Integration
The frontend JavaScript functions are already implemented in the job listing pages:
- `toggleFavorite(jobId)` - Toggle job favorite status
- `toggleShortlist(jobId)` - Toggle job shortlist status
- Search form automatically submits AJAX requests

---

## Production Deployment Notes

### Environment Variables
Set these in your `.env` file for production:
```
APP_ENV=production
APP_DEBUG=false
LOG_LEVEL=warning
CACHE_STORE=redis  # or database
SESSION_DRIVER=redis  # or database
```

### Security Features Enabled
- CSRF protection on all forms
- Rate limiting on API endpoints
- Input sanitization and validation
- Security headers (CSP, HSTS, etc.)
- File upload validation
- SQL injection protection

### Caching
- Job listings cached for 5 minutes
- Categories cached for 10 minutes
- Search results cached for 5 minutes
- Company listings cached for 10 minutes

### Logging
- Security events logged to `storage/logs/security.log`
- Job interactions logged to `storage/logs/jobs.log`
- Application errors logged to `storage/logs/laravel.log`

---

## Support
For technical support or questions about the API, contact the development team.
