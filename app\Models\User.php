<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spa<PERSON>\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Check if the user is an employer
     *
     * @return bool
     */
    public function isEmployer(): bool
    {
        return $this->hasRole('employer');
    }

    /**
     * Check if the user is a job seeker
     *
     * @return bool
     */
    public function isJobSeeker(): bool
    {
        return $this->hasRole('candidate');
    }

    /**
     * Check if the user is a candidate (alias for isJobSeeker)
     *
     * @return bool
     */
    public function isCandidate(): bool
    {
        return $this->hasRole('candidate');
    }

    /**
     * Check if the user is a super admin
     *
     * @return bool
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole('superadmin');
    }

    /**
     * Get the company profile associated with the user
     */
    public function company()
    {
        return $this->hasOne(Company::class);
    }

    /**
     * Get the job seeker profile associated with the user
     */
    public function profile()
    {
        return $this->hasOne(Profile::class);
    }

    /**
     * Get the job listings posted by the employer
     */
    public function jobListings()
    {
        return $this->hasMany(JobListing::class);
    }

    /**
     * Get the job applications submitted by the job seeker
     */
    public function applications()
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * Get the messages sent by the user
     */
    public function sentMessages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    /**
     * Get the messages received by the user
     */
    public function receivedMessages()
    {
        return $this->hasMany(Message::class, 'recipient_id');
    }

    /**
     * Get the job favorites for the user
     */
    public function jobFavorites()
    {
        return $this->hasMany(JobFavorite::class);
    }

    /**
     * Get the job shortlists for the user
     */
    public function jobShortlists()
    {
        return $this->hasMany(JobShortlist::class);
    }

    /**
     * Get the favorited job listings
     */
    public function favoritedJobs()
    {
        return $this->belongsToMany(JobListing::class, 'job_favorites')->withTimestamps();
    }

    /**
     * Get the shortlisted job listings
     */
    public function shortlistedJobs()
    {
        return $this->belongsToMany(JobListing::class, 'job_shortlists')->withTimestamps()->withPivot('notes');
    }

    /**
     * Check if user has favorited a specific job
     */
    public function hasFavorited($jobId)
    {
        return $this->jobFavorites()->where('job_listing_id', $jobId)->exists();
    }

    /**
     * Check if user has shortlisted a specific job
     */
    public function hasShortlisted($jobId)
    {
        return $this->jobShortlists()->where('job_listing_id', $jobId)->exists();
    }
}
