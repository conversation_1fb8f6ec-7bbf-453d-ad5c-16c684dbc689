@extends('frontend.octopus.layouts.master')

@section('content')

	<!-- start banner Area -->
    <section class="banner-area1 relative" id="home">	
        <div class="overlay1 overlay-bg1"></div>
        <div class="container">
            <div class="row d-flex align-items-center justify-content-center">
                <div class="about-content col-lg-12">
                    <h1 class="text-white">
                        {{ $page->title ?? 'Contact' }}
                    </h1>
                    <p class="text-white"><a href="{{url('')}}">Home </a>  <span class="lnr lnr-arrow-right"></span>  <a href="{{url('contact')}}"> Contact</a></p>
                </div>
            </div>
        </div>
    </section>
    <!-- End banner Area -->	

    
			<!-- Start contact-page Area -->
			<section class="contact-page-area section-gap">
    <div class="content py-5  bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6 mb-20">
                    <span class="anchor" id="formContact"></span>
                    <!-- form user info -->
                    <div class="card card-outline-secondary">
                        <div class="card-header">
                            <h3 class="mb-0">General Enquiries</h3>
                        </div>
                        <div class="card-body">
                            <form class="form" role="form" autocomplete="off">
                                <fieldset>
                                    <label for="name2" class="mb-0">Name</label>
                                    <div class="row mb-1">
                                        <div class="col-lg-12">
                                            <input type="text" name="name2" id="name2" class="form-control" required="">
                                        </div>
                                    </div>
                                    <label for="email2" class="mb-0">Email</label>
                                    <div class="row mb-1">
                                        <div class="col-lg-12">
                                            <input type="text" name="email2" id="email2" class="form-control" required="">
                                        </div>
                                    </div>
                                    <label for="email2" class="mb-0">Phone</label>
                                    <div class="row mb-1">
                                        <div class="col-lg-12">
                                            <input type="text" name="phone" id="phone" class="form-control" required="">
                                        </div>
                                    </div>
                                    <label for="message2" class="mb-0">Message</label>
                                    <div class="row mb-1">
                                        <div class="col-lg-12">
                                            <textarea rows="6" name="message2" id="message2" class="form-control" required=""></textarea>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-secondary btn-lg float-right" style=" background-color: #1a75ff!important;">Submit</button>
                                </fieldset>
                            </form>
                        </div>
                    </div>
                    <!-- /form user info -->
                </div>
                <div class="col-md-6 mb-20">
                    <div class="col-lg-12 d-flex">
							<a class="contact-btns" href="#">Submit Your CV</a>
							<a class="contact-btns" href="#">Post New Job</a>
							<a class="contact-btns" href="#">Create New Account</a>
						</div>
						<div class="col-lg-12">
							<form class="form-area " id="myForm" action="mail.php" method="post" class="contact-form text-right">
								<div class="row">	
									<div class="col-lg-12 form-group">
										<input name="name" placeholder="Enter your name" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Enter your name'" class="common-input mb-20 form-control" required="" type="text">
									
										<input name="email" placeholder="Enter email address" pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{1,63}$" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Enter email address'" class="common-input mb-20 form-control" required="" type="email">

										<input name="subject" placeholder="Enter your subject" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Enter your subject'" class="common-input mb-20 form-control" required="" type="text">
										<textarea style="height:115px;" class="common-textarea mt-10 form-control" name="message" placeholder="Messege" onfocus="this.placeholder = ''" onblur="this.placeholder = 'Messege'" required=""></textarea>
										<button class="primary-btn mt-20 text-white" style="float: right;">Send Message</button>
										<div class="mt-20 alert-msg" style="text-align: left;"></div>
									</div>
								</div>
							</form>	
						</div>        
                    <!-- /form user info -->
                </div>
                @if(isset($contactInfo))
                <div class="col-md-3 mb-20">
                    <div class="card card-outline-secondary">
                        <div class="card-header">
                            <h3 class="mb-0">Contact Information</h3>
                        </div>
                        <div class="card-body">
                            @if(isset($contactInfo['email']))
                                <label class="mb-0">Email:</label>
                                <a href="mailto:{{ $contactInfo['email'] }}" style="color:black;">{{ $contactInfo['email'] }}</a><br>
                            @endif
                            @if(isset($contactInfo['phone']))
                                <label class="mb-0">Phone:</label>
                                <a href="tel:{{ $contactInfo['phone'] }}" style="color:black;">{{ $contactInfo['phone'] }}</a><br>
                            @endif
                            @if(isset($contactInfo['address']))
                                <label class="mb-0">Address:</label>
                                <p style="color:black;">{{ $contactInfo['address'] }}</p>
                            @endif
                        </div>
                    </div>
                </div>
                @endif

                <div class="col-md-3 mb-20">
                    <div class="card card-outline-secondary">
                        <div class="card-header">
                            <h3 class="mb-0">Quick Links</h3>
                        </div>
                        <div class="card-body">
                            <a href="{{ route('jobs.all') }}" style="color:black;">Browse Jobs</a><br>
                            <a href="{{ route('frontend.companies') }}" style="color:black;">View Companies</a><br>
                            <a href="{{ url('services') }}" style="color:black;">Our Services</a><br>
                            <a href="{{ url('careers') }}" style="color:black;">Career Guidance</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-20">
                    <span class="anchor" id="formContact"></span>
                    <!-- form user info -->
                    <div class="card card-outline-secondary">
                        <div class="card-header">
                            <h3 class="mb-0">Recruiters</h3>
                        </div>
                        <div class="card-body">
                            <form class="form" role="form" autocomplete="off">
                                <fieldset>                                    
                                    <label for="email2" class="mb-0">Email:</label>
                                    <a href=""  style="color:black;"><EMAIL></a></br>                                    
                                    <label for="email2" class="mb-0">Phone:</label>
                                    <a href=""  style="color:black;">080-0000000</a>                                                                       
                                </fieldset>
                            </form>
                        </div>
                    </div>
                    <!-- /form user info -->
                </div> 
                <div class="col-md-3 mb-20">
                    <span class="anchor" id="formContact"></span>
                    <!-- form user info -->
                    <div class="card card-outline-secondary">
                        <div class="card-header">
                            <h3 class="mb-0">Advertsing Enquiries</h3>
                        </div>
                        <div class="card-body">
                            <form class="form" role="form" autocomplete="off">
                                <fieldset>                                    
                                    <label for="email2" class="mb-0">Email:</label>
                                    <a href=""  style="color:black;"><EMAIL></a></br>                                    
                                    <label for="email2" class="mb-0">Phone:</label>
                                    <a href=""  style="color:black;">080-0000000</a>                                                                       
                                </fieldset>
                            </form>
                        </div>
                    </div>
                    <!-- /form user info -->
                </div>                
                <div class="col-md-12">
                    <span class="anchor" id="formContact"></span>
                    <!-- form user info -->
                    <div class="card card-outline-secondary">
                        <div class="card-header">
                            <h3 class="mb-0">Authorised offices</h3>
                        </div>
                        <div class="card-body">
                            <lable><h4 style="margin-bottom:0px;margin-top:10px;">Bangalore Head Office</h4></lable></br>
                            <a href=""  style="color:black;">
                                <span class="fa fa-map-marker fa-lg"></span> 1637/19, 2nd floor, 7th Cross, 80 Ft Main road, Srinivasnagara , 
                                Bank Colony, </br> Banashankari 1st Stage, Bangalore-560050, Karnataka, India
                            </a>
                            <hr>
                            <lable><h4 style="margin-bottom:0px;margin-top:20px;">Mysore Office</h4></lable></br>
                            <a href=""  style="color:black;">
                                <span class="fa fa-map-marker fa-lg"></span> 1637/19, 2nd floor, 7th Cross, 80 Ft Main road, Srinivasnagara , 
                                Bank Colony, </br> Banashankari 1st Stage, Mysore-560050, Karnataka, India
                            </a>
                            <hr>
                            <lable><h4 style="margin-bottom:0px;margin-top:20px;">Delhi Office</h4></lable></br>
                            <a href=""  style="color:black;">
                                <span class="fa fa-map-marker fa-lg"></span> 1637/19, 2nd floor, 7th Cross, 80 Ft Main road, Srinivasnagara , 
                                Bank Colony, </br> Banashankari 1st Stage, Delhi-560050, Delhi, India
                            </a>
                            <hr>
                            <lable><h4 style="margin-bottom:0px;margin-top:20px;">Indore Office</h4></lable></br>
                            <a href="" style="color:black;">
                                <span  class="fa fa-map-marker fa-lg"></span> 1637/19, 2nd floor, 7th Cross, 80 Ft Main road, Srinivasnagara , 
                                Bank Colony, </br> Banashankari 1st Stage, Indore-560050, Indore, India
                            </a>                  
                        </div>
                    </div>
                    <!-- /form user info -->
                </div>
            </div>
        </div>
    </div>
</section>
			<!-- End contact-page Area -->
               

    

@endsection