<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class JobListing extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'company_id',
        'title',
        'slug',
        'description',
        'responsibilities',
        'requirements',
        'benefits',
        'job_type',
        'location',
        'remote_option',
        'salary_min',
        'salary_max',
        'salary_currency',
        'salary_period',
        'application_deadline',
        'experience_level',
        'education_level',
        'skills_required',
        'application_url',
        'contact_email',
        'status',
        'is_featured',
        'views_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'responsibilities' => 'array',
        'requirements' => 'array',
        'benefits' => 'array',
        'skills_required' => 'array',
        'application_deadline' => 'date',
        'remote_option' => 'boolean',
        'is_featured' => 'boolean',
        'salary_min' => 'float',
        'salary_max' => 'float',
    ];

    /**
     * Get the user that posted the job listing.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the company that posted the job listing.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the applications for the job listing.
     */
    public function applications(): HasMany
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * Get the categories for the job listing.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class);
    }

    /**
     * Get the favorites for the job listing.
     */
    public function favorites(): HasMany
    {
        return $this->hasMany(JobFavorite::class);
    }

    /**
     * Get the shortlists for the job listing.
     */
    public function shortlists(): HasMany
    {
        return $this->hasMany(JobShortlist::class);
    }

    /**
     * Get the users who favorited this job.
     */
    public function favoritedByUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'job_favorites')->withTimestamps();
    }

    /**
     * Get the users who shortlisted this job.
     */
    public function shortlistedByUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'job_shortlists')->withTimestamps()->withPivot('notes');
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($jobListing) {
            if (empty($jobListing->slug)) {
                $jobListing->slug = static::generateUniqueSlug($jobListing->title);
            }
        });

        static::updating(function ($jobListing) {
            if ($jobListing->isDirty('title') && empty($jobListing->slug)) {
                $jobListing->slug = static::generateUniqueSlug($jobListing->title);
            }
        });
    }

    /**
     * Generate a unique slug for the job listing.
     */
    protected static function generateUniqueSlug($title, $id = null)
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)->when($id, function ($query, $id) {
            return $query->where('id', '!=', $id);
        })->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
}
