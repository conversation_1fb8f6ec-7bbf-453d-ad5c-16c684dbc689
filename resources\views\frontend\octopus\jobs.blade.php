@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">
                    {{ $page->title ?? 'Jobs' }}
                </h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>{{ $page->title ?? 'Jobs' }}</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-3 sidebar" id="displayNoneMobile">
                <div style="padding:5px; border:1px solid lightgrey">
                    <h3 style="text-align:center; margin-top:10px">Filter Options</h3>
                    <div class="single-slidebar" id="sidebar">
                        <h4 style="margin-bottom:5px">Location</h4><br>
                        <input type="text" id="searchCasual" readonly class="form-control-plaintext" value="Search Locations"><br>

                        <h4 style="margin-bottom:5px">Business Type</h4><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">All</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Individual</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">NGO</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Government</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Public Limited</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Private Limited</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Proprietory</span></a><br>

                        <h4 style="margin-bottom:5px">Job Type</h4><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Full Time</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Part Time</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Contract</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Freelance</span></a><br>
                        <a><input class="btn" id="ntb" type="checkbox" href="#"><span class="ml-5">Internship</span></a><br>
                    </div>
                </div>
            </div>

            <div class="col-lg-9 post-list">
                @if(isset($page->content))
                    <div class="single-post">
                        <div class="details">
                            {!! $page->content !!}
                        </div>
                    </div>
                @endif

                @php
                    // Get jobs related to this page
                    $searchTerm = str_replace(['_', '-'], ' ', $page->slug ?? '');
                    $jobs = \App\Models\JobListing::with(['company', 'categories'])
                        ->where('status', 'published')
                        ->where(function($query) use ($searchTerm) {
                            $query->where('title', 'LIKE', "%{$searchTerm}%")
                                  ->orWhere('description', 'LIKE', "%{$searchTerm}%")
                                  ->orWhere('job_type', 'LIKE', "%{$searchTerm}%")
                                  ->orWhereHas('categories', function($q) use ($searchTerm) {
                                      $q->where('name', 'LIKE', "%{$searchTerm}%");
                                  });
                        })
                        ->latest()
                        ->take(10)
                        ->get();
                @endphp

                @if($jobs->count() > 0)
                    <div class="single-post">
                        <div class="details">
                            <h4>Related Job Opportunities</h4>
                        </div>
                    </div>

                    @foreach($jobs as $job)
                        <div class="single-post d-flex flex-row">
                            <div class="thumb">
                                @if($job->company && $job->company->logo)
                                    <img src="{{ $job->company->logo }}" alt="{{ $job->company->name }}">
                                @else
                                    <img src="{{url('theme/web/img/post.png')}}" alt="">
                                @endif
                                <ul class="tags">
                                    @foreach($job->categories as $category)
                                        <li><a href="{{ route('category.jobs', $category->slug) }}">{{ $category->name }}</a></li>
                                    @endforeach
                                </ul>
                            </div>
                            <div class="details">
                                <a href="{{ route('frontend.job.show', $job->slug) }}">
                                    <h6>{{ $job->title }}</h6>
                                </a>
                                <p>{{ Str::limit($job->description, 150) }}</p>
                                <p class="address">
                                    <span class="lnr lnr-map"></span> {{ $job->location }}
                                </p>
                                <p class="address">
                                    <span class="lnr lnr-database"></span> {{ ucfirst($job->job_type) }}
                                </p>
                                @if($job->company)
                                    <p class="address">
                                        <span class="lnr lnr-apartment"></span> 
                                        <a href="{{ route('frontend.company', $job->company->slug) }}">{{ $job->company->name }}</a>
                                    </p>
                                @endif
                            </div>
                        </div>
                    @endforeach

                    <div class="single-post">
                        <div class="details text-center">
                            <a href="{{ url('jobs') }}" class="primary-btn">View All Jobs</a>
                        </div>
                    </div>
                @endif

                @if(isset($page->data) && is_array($page->data))
                    @foreach($page->data as $key => $section)
                        @if($key === 'sections' && is_array($section))
                            @foreach($section as $sectionData)
                                <div class="single-post">
                                    <div class="details">
                                        @if(isset($sectionData['title']))
                                            <h4>{{ $sectionData['title'] }}</h4>
                                        @endif
                                        
                                        @if(isset($sectionData['items']) && is_array($sectionData['items']))
                                            <div class="row">
                                                @foreach($sectionData['items'] as $item)
                                                    <div class="col-md-6 mb-3">
                                                        <div class="single-post d-flex flex-row">
                                                            @if(isset($item['image']))
                                                                <div class="thumb">
                                                                    <img src="{{ $item['image'] }}" alt="{{ $item['title'] ?? '' }}">
                                                                </div>
                                                            @endif
                                                            <div class="details">
                                                                @if(isset($item['title']))
                                                                    <h6>
                                                                        @if(isset($item['link']))
                                                                            <a href="{{ $item['link'] }}">{{ $item['title'] }}</a>
                                                                        @else
                                                                            {{ $item['title'] }}
                                                                        @endif
                                                                    </h6>
                                                                @endif
                                                                @if(isset($item['description']))
                                                                    <p>{{ $item['description'] }}</p>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    @endforeach
                @endif
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
@endsection
