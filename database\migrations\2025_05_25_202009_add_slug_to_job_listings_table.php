<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if slug column already exists
        if (!Schema::hasColumn('job_listings', 'slug')) {
            Schema::table('job_listings', function (Blueprint $table) {
                $table->string('slug')->nullable()->after('title');
            });
        }

        // Generate slugs for existing job listings
        $jobListings = \App\Models\JobListing::all();
        foreach ($jobListings as $job) {
            $slug = \Illuminate\Support\Str::slug($job->title);
            $originalSlug = $slug;
            $counter = 1;

            // Ensure unique slug
            while (\App\Models\JobListing::where('slug', $slug)->where('id', '!=', $job->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $job->update(['slug' => $slug]);
        }

        // Now make the slug column unique and not nullable if it exists
        if (Schema::hasColumn('job_listings', 'slug')) {
            Schema::table('job_listings', function (Blueprint $table) {
                $table->string('slug')->unique()->nullable(false)->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_listings', function (Blueprint $table) {
            $table->dropColumn('slug');
        });
    }
};
