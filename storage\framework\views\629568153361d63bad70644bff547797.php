<?php $__env->startSection('content'); ?>
    <!-- start banner Area -->
    <section class="banner-area relative" id="home">
        <div class="overlay overlay-bg"></div>
        <div class="container">
            <div id="bnr1" class="row fullscreen d-flex align-items-center justify-content-center" style="height: 250px; !important;">
                <div class="banner-content col-lg-12">

                    <form action="<?php echo e(route('jobs.search')); ?>" method="GET" class="search-form-area" id="jobSearchForm">
                        <div class="row justify-content-center form-wrap">
                            <div class="col-lg-6 form-cols">
                                <input type="text" class="form-control" name="keywords" placeholder="Enter Keywords, Skills, Designation" value="<?php echo e(request('keywords')); ?>">
                            </div>

                             <div class="col-lg-4 form-cols">
                                <input type="text" class="form-control" name="location" placeholder="Enter Location" value="<?php echo e(request('location')); ?>">
                            </div>

                            <div class="col-lg-2 form-cols">
                                <button type="submit" class="btn btn-info" style="color:white">
                                    <span class="lnr lnr-magnifier"> Search </span>
                                </button>
                                </br>
                                <a id="sMore" href="#"> Search options </a>
                            </div>
                        </div>

                        <!-- Hidden inputs for advanced search -->
                        <input type="hidden" name="job_type" id="job_type_input" value="<?php echo e(request('job_type')); ?>">
                        <input type="hidden" name="remote_option" id="remote_option_input" value="<?php echo e(request('remote_option')); ?>">

                        <div id="panelSMore" style="border-top: 0px solid #b8aaf3; display: none;" class="row justify-content-center form-wrap">
                            <div class="col-lg-12 form-cols">
                                <ul class="nav nav-tabs  row justify-content-center">
                                    <li id="list" class="active"><a data-toggle="tab" href="#menu1">Employment Types</a></li>
                                    <li id="list"><a data-toggle="tab" href="#menu2">Salary - Remuneration</a></li>
                                    <li id="list"><a data-toggle="tab" href="#menu3">Sort by Time</a></li>
                                    <li id="list"><a data-toggle="tab" href="#menu4">Work Location Type</a></li>
                                </ul>
                                <div class="tab-content">
                                    <div id="menu1" class="tab-pane fade in active">
                                        <div class="btn-group nav-tabs">
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="">All</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="full_time">Full-time</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="part_time">Part-time</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="casual">Casual</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="contract">Contract</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="internship">Internship</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="online">Online</button>
                                            <button type="button" class="btn btn-primary job-type-btn" data-type="freelance">Freelance</button>
                                        </div>
                                    </div>
                                    <div id="menu2" class="tab-pane fade">
                                        <div class="row justify-content-center ">
                                            <div class="col-lg-4">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary">
                                                         <select name="salary_min" class="form-control">
                                                            <option value="">Minimum Salary</option>
                                                            <option value="0" <?php echo e(request('salary_min') == '0' ? 'selected' : ''); ?>>Any</option>
                                                            <option value="50000" <?php echo e(request('salary_min') == '50000' ? 'selected' : ''); ?>>₹50,000</option>
                                                            <option value="100000" <?php echo e(request('salary_min') == '100000' ? 'selected' : ''); ?>>₹1,00,000</option>
                                                            <option value="200000" <?php echo e(request('salary_min') == '200000' ? 'selected' : ''); ?>>₹2,00,000</option>
                                                            <option value="300000" <?php echo e(request('salary_min') == '300000' ? 'selected' : ''); ?>>₹3,00,000</option>
                                                            <option value="500000" <?php echo e(request('salary_min') == '500000' ? 'selected' : ''); ?>>₹5,00,000</option>
                                                            <option value="700000" <?php echo e(request('salary_min') == '700000' ? 'selected' : ''); ?>>₹7,00,000</option>
                                                            <option value="1000000" <?php echo e(request('salary_min') == '1000000' ? 'selected' : ''); ?>>₹10,00,000</option>
                                                            <option value="1500000" <?php echo e(request('salary_min') == '1500000' ? 'selected' : ''); ?>>₹15,00,000+</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary">
                                                        <select name="salary_max" class="form-control">
                                                            <option value="">Maximum Salary</option>
                                                            <option value="100000" <?php echo e(request('salary_max') == '100000' ? 'selected' : ''); ?>>₹1,00,000</option>
                                                            <option value="200000" <?php echo e(request('salary_max') == '200000' ? 'selected' : ''); ?>>₹2,00,000</option>
                                                            <option value="300000" <?php echo e(request('salary_max') == '300000' ? 'selected' : ''); ?>>₹3,00,000</option>
                                                            <option value="500000" <?php echo e(request('salary_max') == '500000' ? 'selected' : ''); ?>>₹5,00,000</option>
                                                            <option value="700000" <?php echo e(request('salary_max') == '700000' ? 'selected' : ''); ?>>₹7,00,000</option>
                                                            <option value="1000000" <?php echo e(request('salary_max') == '1000000' ? 'selected' : ''); ?>>₹10,00,000</option>
                                                            <option value="1500000" <?php echo e(request('salary_max') == '1500000' ? 'selected' : ''); ?>>₹15,00,000</option>
                                                            <option value="2000000" <?php echo e(request('salary_max') == '2000000' ? 'selected' : ''); ?>>₹20,00,000+</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="menu3" class="tab-pane fade">
                                        <div class="row justify-content-center ">
                                            <div class="col-lg-4" id="meeting1">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary" id="meeting" >
                                                         <select name="date_posted" class="form-control" style="height: 40px; width: 160px;">
                                                            <option value="">All Time</option>
                                                            <option value="1" <?php echo e(request('date_posted') == '1' ? 'selected' : ''); ?>>Last 24 hours</option>
                                                            <option value="7" <?php echo e(request('date_posted') == '7' ? 'selected' : ''); ?>>Last 7 days</option>
                                                            <option value="30" <?php echo e(request('date_posted') == '30' ? 'selected' : ''); ?>>Last 30 days</option>
                                                            <option value="90" <?php echo e(request('date_posted') == '90' ? 'selected' : ''); ?>>Last 3 months</option>
                                                            <option value="180" <?php echo e(request('date_posted') == '180' ? 'selected' : ''); ?>>Last 6 months</option>
                                                        </select>
                                                        <!-- <a style="font-size:20px; color:white;">OR </a> -->
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4" id="meeting2">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary" >
                                                        <label for="date_from" style="color:white; margin-right: 10px">From:</label>
                                                        <input name="date_from" type="date" class="form-control" value="<?php echo e(request('date_from')); ?>"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4" id="meeting3">
                                                <div class="btn-group btn-group-justified">
                                                    <div class="salary" >
                                                        <label for="date_to" style="color:white; margin-right: 10px">To:</label>
                                                        <input name="date_to" type="date" class="form-control" value="<?php echo e(request('date_to')); ?>"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="menu4" class="tab-pane fade">
                                        <div>
                                            <button type="button" class="btn btn-primary remote-btn" data-remote="">All</button>
                                            <button type="button" class="btn btn-primary remote-btn" data-remote="0">OnSite</button>
                                            <button type="button" class="btn btn-primary remote-btn" data-remote="1">Remote</button>
                                            <button type="button" class="btn btn-primary remote-btn" data-remote="hybrid">Hybrid</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
           </div>
        </div>
    </section>
    <!-- End banner Area -->
    
     <!-- Start feature-cat Area -->
    <section class="feature-cat-area pt-10" id="category">
        <div class="container">
            <div class="row d-flex justify-content-center">
                <div class="menu-content pb-10 col-lg-10" >
                    <div class="title text-center">
                        <h4 class="mb-10">Featured Job Categories</h4>
                        
                    </div>
                </div>
            </div>
            <div class="row">
                <?php if(isset($featuredCategories) && $featuredCategories->count() > 0): ?>
                    <?php $__currentLoopData = $featuredCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <div class="single-fcat">
                                <a href="<?php echo e(route('category.jobs', $category->slug)); ?>">
                                    <img id="imgfcat" src="<?php echo e(url('theme/web/img/o' . (($loop->index % 6) + 1) . '.png')); ?>" alt="<?php echo e($category->name); ?>">
                                </a>
                                <p><a href="<?php echo e(route('category.jobs', $category->slug)); ?>"><?php echo e($category->name); ?></a></p>
                                <?php if($category->job_listings_count > 0): ?>
                                    <small class="text-muted">(<?php echo e($category->job_listings_count); ?> jobs)</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p class="text-muted">No categories available at the moment.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
    <!-- end feature-cat Area -->

    
    
       <!-- Start post Area -->
   <section class="post-area section-gap">
        <div class="container">
            <div class="row justify-content-center d-flex">
                <div class="col-lg-3 sidebar">                                            
                    <div class="single-slidebar" id="moresidebar">
                        <h4 style="text-align:center;margin-bottom: 10px;">Top Recruiters</h4>
                        <?php if(isset($topRecruiters) && $topRecruiters->count() > 0): ?>
                            <?php $__currentLoopData = $topRecruiters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recruiter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="recruiter-item mb-2">
                                    <?php if($recruiter->logo): ?>
                                        <img class="mimage" src="<?php echo e(asset('storage/' . $recruiter->logo)); ?>" alt="<?php echo e($recruiter->name); ?>">
                                    <?php else: ?>
                                        <div class="company-placeholder"><?php echo e(substr($recruiter->name, 0, 2)); ?></div>
                                    <?php endif; ?>
                                    <p class="company-name"><?php echo e($recruiter->name); ?></p>
                                    <small class="text-muted"><?php echo e($recruiter->job_listings_count); ?> jobs</small>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <p class="text-muted text-center">No recruiters available.</p>
                        <?php endif; ?>
                    </div>


                    <div class="single-slidebar" id="moresidebar">
                        <h4 style="text-align:center;margin-bottom: 10px;">Jobs by Location</h4>
                        <?php if(isset($jobsByLocation) && $jobsByLocation->count() > 0): ?>
                            <ul class="list-unstyled">
                                <?php $__currentLoopData = $jobsByLocation; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="mb-2">
                                        <a href="<?php echo e(route('jobs.search', ['location' => $location->location])); ?>" class="text-decoration-none">
                                            <i class="fa fa-map-marker"></i> <?php echo e($location->location); ?>

                                            <span class="badge badge-primary float-right"><?php echo e($location->count); ?></span>
                                        </a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-muted text-center">No location data available.</p>
                        <?php endif; ?>
                    </div>







                </div>

                <!--Job ad display section-->

                <div class="col-lg-6 post-list">
                    <h4 class="mb-4">Latest Jobs</h4>

                    <?php if(isset($recentJobs) && $recentJobs->count() > 0): ?>
                        <?php $__currentLoopData = $recentJobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="single-post d-flex flex-row mb-4">
                                <div class="details col-lg-12 col-md-12 col-sm-12">
                                    <div class="title d-flex flex-row justify-content-between">
                                        <div class="titles">
                                            <a href="<?php echo e(route('frontend.job.show', $job->slug)); ?>">
                                                <h3 style="margin-top:10px;" class="main-color"><?php echo e($job->title); ?></h3>
                                            </a>
                                            <h6><b><?php echo e($job->company ? $job->company->name : 'Company Name'); ?></b></h6>
                                        </div>
                                    </div>
                                    <div class="title justify-content-between">
                                        <div class="titles mb-15">
                                            <a id="locate1"><b>Exp:</b> <?php echo e($job->experience_level ?? 'Not specified'); ?></a>
                                            <a><span class="fa fa-map-marker locate"></span> <?php echo e($job->location); ?></a>
                                            <?php if($job->salary_min && $job->salary_max): ?>
                                                <a><b>Salary:</b> ₹<?php echo e(number_format($job->salary_min)); ?> - ₹<?php echo e(number_format($job->salary_max)); ?></a>
                                            <?php endif; ?>
                                        </div>
                                        <?php if($job->skills_required): ?>
                                            <h5 style="text-align:justify;"><b>KeySkills:</b><span style="line-height: 1.9;">
                                                <?php echo e(is_array($job->skills_required) ? implode(', ', array_slice($job->skills_required, 0, 5)) : $job->skills_required); ?>

                                                <?php if(is_array($job->skills_required) && count($job->skills_required) > 5): ?>...<?php endif; ?>
                                            </span></h5>
                                        <?php endif; ?>
                                        <h5 style="text-align:justify;"><b>Description:</b><span style="line-height: 1.9;">
                                            <?php echo e(Str::limit(strip_tags($job->description), 100)); ?>

                                        </span>
                                        <a href="<?php echo e(route('frontend.job.show', $job->slug)); ?>" class="main-color">More...</a></h5>
                                    </div>
                                    <ul class="btns">
                                        <li class="ml-5 float-right "><a href="<?php echo e(route('frontend.job.show', $job->slug)); ?>">Apply</a></li>
                                        <li class="ml-5 float-right ">
                                            <a href="#" onclick="toggleShortlist(<?php echo e($job->id); ?>)"
                                               style="color: <?php echo e(isset($job->is_shortlisted) && $job->is_shortlisted ? '#28a745' : '#6c757d'); ?>">
                                                <?php echo e(isset($job->is_shortlisted) && $job->is_shortlisted ? 'Shortlisted' : 'Shortlist'); ?>

                                            </a>
                                        </li>
                                        <li id="view">
                                            <a href="#" onclick="toggleFavorite(<?php echo e($job->id); ?>)">
                                                <i style="color:#ff0066;" class="fa <?php echo e(isset($job->is_favorited) && $job->is_favorited ? 'fa-heart' : 'fa-heart-o'); ?> fa-lg"></i>
                                            </a>
                                        </li>
                                        <li id="view"><a style="color:black;"><i style="color:black;" class="fa fa-eye fa-lg"></i> <?php echo e($job->views_count ?? 0); ?></a></li>
                                    </ul>
                                </div>
                                <div class="none">
                                    <ul>
                                        <li>
                                            <?php if($job->company && $job->company->logo): ?>
                                                <img class="indeximage" src="<?php echo e(asset('storage/' . $job->company->logo)); ?>" alt="<?php echo e($job->company->name); ?>">
                                            <?php else: ?>
                                                <img class="indeximage" src="<?php echo e(url('theme/web/img/company.jpg')); ?>" alt="Company">
                                            <?php endif; ?>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <div class="text-center mt-4">
                            <a class="text-uppercase loadmore-btn mx-auto d-block loadmore" href="<?php echo e(route('jobs.all')); ?>">View All Jobs</a>
                        </div>
                    <?php else: ?>
                        <div class="text-center">
                            <p class="text-muted">No jobs available at the moment.</p>
                            <a href="<?php echo e(route('jobs.all')); ?>" class="btn btn-primary">Browse All Jobs</a>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-lg-3 sidebar">
                    <div class="single-slidebar">
                        <h4>Jobs by Location</h4>
                        <?php if(isset($jobsByLocation) && $jobsByLocation->count() > 0): ?>
                            <ul class="cat-list">
                                <?php $__currentLoopData = $jobsByLocation; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li style="margin: 0px;">
                                        <a class="justify-content-between d-flex" href="<?php echo e(route('jobs.search', ['location' => $location->location])); ?>">
                                            <p><?php echo e($location->location); ?></p>
                                            <span><?php echo e(number_format($location->count)); ?></span>
                                        </a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-muted text-center">No location data available.</p>
                        <?php endif; ?>
                    </div>
                    <div class="single-slidebar">
                        <h4>Featured Jobs</h4>
                        <?php if(isset($recentJobs) && $recentJobs->count() > 0): ?>
                            <div class="active-relatedjob-carusel">
                                <?php $__currentLoopData = $recentJobs->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $featuredJob): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="single-rated mb-4">
                                        <?php if($featuredJob->company && $featuredJob->company->logo): ?>
                                            <img class="img-fluid" src="<?php echo e(asset('storage/' . $featuredJob->company->logo)); ?>" alt="<?php echo e($featuredJob->company->name); ?>">
                                        <?php else: ?>
                                            <img class="img-fluid" src="<?php echo e(url('theme/web/img/r1.jpg')); ?>" alt="Company">
                                        <?php endif; ?>
                                        <a href="<?php echo e(route('frontend.job.show', $featuredJob->slug)); ?>"><h4><?php echo e($featuredJob->title); ?></h4></a>
                                        <h6><?php echo e($featuredJob->company ? $featuredJob->company->name : 'Company Name'); ?></h6>
                                        <p><?php echo e(Str::limit(strip_tags($featuredJob->description), 100)); ?></p>
                                        <h5>Job Nature: <?php echo e(ucfirst(str_replace('_', ' ', $featuredJob->job_type))); ?></h5>
                                        <p class="address"><span class="lnr lnr-map"></span> <?php echo e($featuredJob->location); ?></p>
                                        <?php if($featuredJob->salary_min && $featuredJob->salary_max): ?>
                                            <p class="address"><span class="lnr lnr-database"></span> ₹<?php echo e(number_format($featuredJob->salary_min)); ?> - ₹<?php echo e(number_format($featuredJob->salary_max)); ?></p>
                                        <?php endif; ?>
                                        <a href="<?php echo e(route('frontend.job.show', $featuredJob->slug)); ?>" class="btns text-uppercase">Apply job</a>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No featured jobs available.</p>
                        <?php endif; ?>
                    </div>

                </div>
            </div>
        </div>
    </section>
    <!-- End post Area -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    $('#sMore').click(function(e){
        e.preventDefault();
        if($('#panelSMore').is(":visible")){
            $('#panelSMore').slideUp(800);
        } else {
            $('#panelSMore').slideDown(800);
        }
    });

    // Set active states based on current request values
    var currentJobType = '<?php echo e(request("job_type")); ?>';
    var currentRemoteOption = '<?php echo e(request("remote_option")); ?>';

    if(currentJobType) {
        $('.job-type-btn[data-type="' + currentJobType + '"]').addClass('active');
    } else {
        $('.job-type-btn[data-type=""]').addClass('active');
    }

    if(currentRemoteOption !== '') {
        $('.remote-btn[data-remote="' + currentRemoteOption + '"]').addClass('active');
    } else {
        $('.remote-btn[data-remote=""]').addClass('active');
    }

    // Job type filter functionality
    $('.job-type-btn').click(function() {
        $('.job-type-btn').removeClass('active');
        $(this).addClass('active');
        $('#job_type_input').val($(this).data('type'));
    });

    // Remote option filter functionality
    $('.remote-btn').click(function() {
        $('.remote-btn').removeClass('active');
        $(this).addClass('active');
        $('#remote_option_input').val($(this).data('remote'));
    });
});

// Shortlist functionality
function toggleShortlist(jobId) {
    <?php if(auth()->guard()->check()): ?>
        // Add AJAX call to handle shortlisting
        $.ajax({
            url: '/jobs/' + jobId + '/shortlist',
            type: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if(response.success) {
                    // Toggle shortlist button text and color
                    var shortlistLink = $('a[onclick="toggleShortlist(' + jobId + ')"]');
                    if(response.shortlisted) {
                        shortlistLink.text('Shortlisted').css('color', '#28a745');
                    } else {
                        shortlistLink.text('Shortlist').css('color', '#6c757d');
                    }
                    // Show success message
                    alert(response.message);
                }
            },
            error: function(xhr) {
                if(xhr.status === 401) {
                    alert('Please login to shortlist jobs.');
                    window.location.href = '<?php echo e(route("login")); ?>';
                } else {
                    alert('Please try again later.');
                }
            }
        });
    <?php else: ?>
        alert('Please login to shortlist jobs.');
        window.location.href = '<?php echo e(route("login")); ?>';
    <?php endif; ?>
}

// Favorite functionality
function toggleFavorite(jobId) {
    <?php if(auth()->guard()->check()): ?>
        // Add AJAX call to handle favorites
        $.ajax({
            url: '/jobs/' + jobId + '/favorite',
            type: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if(response.success) {
                    // Toggle heart icon
                    var heartIcon = $('a[onclick="toggleFavorite(' + jobId + ')"] i');
                    if(response.favorited) {
                        heartIcon.removeClass('fa-heart-o').addClass('fa-heart');
                    } else {
                        heartIcon.removeClass('fa-heart').addClass('fa-heart-o');
                    }
                    // Show success message
                    alert(response.message);
                }
            },
            error: function(xhr) {
                if(xhr.status === 401) {
                    alert('Please login to favorite jobs.');
                    window.location.href = '<?php echo e(route("login")); ?>';
                } else {
                    alert('Please try again later.');
                }
            }
        });
    <?php else: ?>
        alert('Please login to favorite jobs.');
        window.location.href = '<?php echo e(route("login")); ?>';
    <?php endif; ?>
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.octopus.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\jo\joboctopus_new\resources\views/frontend/octopus/index.blade.php ENDPATH**/ ?>