<?php $__env->startSection('content'); ?>
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">
                    <?php echo e($page->title ?? 'Services'); ?>

                </h1>
                <p class="text-white link-nav">
                    <a href="<?php echo e(url('')); ?>">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span><?php echo e($page->title ?? 'Services'); ?></span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                <?php if(isset($page->content)): ?>
                    <div class="single-post">
                        <div class="details">
                            <?php echo $page->content; ?>

                        </div>
                    </div>
                <?php endif; ?>

                <?php if(isset($page->data) && is_array($page->data)): ?>
                    <?php $__currentLoopData = $page->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($key === 'sections' && is_array($section)): ?>
                            <?php $__currentLoopData = $section; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="single-post">
                                    <div class="details">
                                        <?php if(isset($sectionData['title'])): ?>
                                            <h4><?php echo e($sectionData['title']); ?></h4>
                                        <?php endif; ?>
                                        
                                        <?php if(isset($sectionData['items']) && is_array($sectionData['items'])): ?>
                                            <div class="row">
                                                <?php $__currentLoopData = $sectionData['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-md-6 mb-4">
                                                        <div class="single-post d-flex flex-row">
                                                            <div class="thumb">
                                                                <?php if(isset($item['image'])): ?>
                                                                    <img src="<?php echo e($item['image']); ?>" alt="<?php echo e($item['title'] ?? ''); ?>">
                                                                <?php else: ?>
                                                                    <img src="<?php echo e(url('theme/web/img/post.png')); ?>" alt="">
                                                                <?php endif; ?>
                                                                <?php if(isset($item['category'])): ?>
                                                                    <ul class="tags">
                                                                        <li><a href="#"><?php echo e($item['category']); ?></a></li>
                                                                    </ul>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div class="details">
                                                                <?php if(isset($item['title'])): ?>
                                                                    <h6>
                                                                        <?php if(isset($item['link'])): ?>
                                                                            <a href="<?php echo e($item['link']); ?>"><?php echo e($item['title']); ?></a>
                                                                        <?php else: ?>
                                                                            <?php echo e($item['title']); ?>

                                                                        <?php endif; ?>
                                                                    </h6>
                                                                <?php endif; ?>
                                                                <?php if(isset($item['description'])): ?>
                                                                    <p><?php echo e($item['description']); ?></p>
                                                                <?php endif; ?>
                                                                <?php if(isset($item['price'])): ?>
                                                                    <p class="address">
                                                                        <span class="lnr lnr-tag"></span> <?php echo e($item['price']); ?>

                                                                    </p>
                                                                <?php endif; ?>
                                                                <?php if(isset($item['duration'])): ?>
                                                                    <p class="address">
                                                                        <span class="lnr lnr-clock"></span> <?php echo e($item['duration']); ?>

                                                                    </p>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php elseif($key === 'contact_info' && is_array($section)): ?>
                            <div class="single-post">
                                <div class="details">
                                    <h4>Contact Information</h4>
                                    <?php if(isset($section['email'])): ?>
                                        <p class="address">
                                            <span class="lnr lnr-envelope"></span> <?php echo e($section['email']); ?>

                                        </p>
                                    <?php endif; ?>
                                    <?php if(isset($section['phone'])): ?>
                                        <p class="address">
                                            <span class="lnr lnr-phone"></span> <?php echo e($section['phone']); ?>

                                        </p>
                                    <?php endif; ?>
                                    <?php if(isset($section['address'])): ?>
                                        <p class="address">
                                            <span class="lnr lnr-map"></span> <?php echo e($section['address']); ?>

                                        </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>

                <!-- Call to Action -->
                <div class="single-post">
                    <div class="details text-center">
                        <h4>Ready to Get Started?</h4>
                        <p>Take the next step in your career journey with our professional services.</p>
                        <a href="<?php echo e(url('contact')); ?>" class="primary-btn">Contact Us</a>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4 sidebar">
                <div class="single-slidebar">
                    <h4>Our Services</h4>
                    <div class="blog-list">
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="<?php echo e(url('service_templates')); ?>">
                                    <h6>Resume Templates</h6>
                                </a>
                                <p>Professional resume templates for all industries</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="<?php echo e(url('cover_letter')); ?>">
                                    <h6>Cover Letters</h6>
                                </a>
                                <p>Compelling cover letter examples and templates</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="<?php echo e(url('training')); ?>">
                                    <h6>Training Programs</h6>
                                </a>
                                <p>Skill development and certification courses</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="<?php echo e(url('counselling')); ?>">
                                    <h6>Career Counselling</h6>
                                </a>
                                <p>Expert guidance for your career decisions</p>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if(isset($page->data['navigation']) && is_array($page->data['navigation'])): ?>
                    <div class="single-slidebar">
                        <h4>Quick Links</h4>
                        <div class="blog-list">
                            <?php $__currentLoopData = $page->data['navigation']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $nav): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="single-blog d-flex flex-row">
                                    <div class="details">
                                        <a href="<?php echo e($nav['link']); ?>">
                                            <h6><?php echo e($nav['title']); ?></h6>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.octopus.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\jo\joboctopus_new\resources\views/frontend/octopus/service.blade.php ENDPATH**/ ?>