<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\JobListing;
use App\Models\Category;
use App\Models\Company;

class DatabaseOptimizationService
{
    /**
     * Get optimized job listings with proper eager loading
     */
    public function getOptimizedJobListings($limit = 10, $filters = [])
    {
        $cacheKey = 'job_listings_' . md5(serialize($filters)) . '_' . $limit;
        
        return Cache::remember($cacheKey, 300, function () use ($limit, $filters) {
            $query = JobListing::with([
                'company:id,name,logo,slug',
                'categories:id,name,slug'
            ])
            ->select([
                'id', 'title', 'slug', 'description', 'location', 'job_type',
                'salary_min', 'salary_max', 'experience_level', 'remote_option',
                'views_count', 'created_at', 'company_id', 'status'
            ])
            ->where('status', 'published');

            // Apply filters
            if (!empty($filters['keywords'])) {
                $query->where(function ($q) use ($filters) {
                    $q->where('title', 'like', '%' . $filters['keywords'] . '%')
                      ->orWhere('description', 'like', '%' . $filters['keywords'] . '%');
                });
            }

            if (!empty($filters['location'])) {
                $query->where('location', 'like', '%' . $filters['location'] . '%');
            }

            if (!empty($filters['job_type'])) {
                $query->where('job_type', $filters['job_type']);
            }

            if (!empty($filters['company_id'])) {
                $query->where('company_id', $filters['company_id']);
            }

            return $query->latest()->limit($limit)->get();
        });
    }

    /**
     * Get optimized categories with job counts
     */
    public function getOptimizedCategories($limit = null)
    {
        $cacheKey = 'categories_with_counts_' . ($limit ?? 'all');
        
        return Cache::remember($cacheKey, 600, function () use ($limit) {
            $query = Category::select(['id', 'name', 'slug', 'parent_id'])
                ->whereNull('parent_id')
                ->withCount(['jobListings' => function ($query) {
                    $query->where('status', 'published');
                }])
                ->orderBy('job_listings_count', 'desc');

            if ($limit) {
                $query->limit($limit);
            }

            return $query->get();
        });
    }

    /**
     * Get optimized companies with job counts
     */
    public function getOptimizedCompanies($limit = null)
    {
        $cacheKey = 'companies_with_counts_' . ($limit ?? 'all');
        
        return Cache::remember($cacheKey, 600, function () use ($limit) {
            $query = Company::select(['id', 'name', 'slug', 'logo'])
                ->withCount(['jobListings' => function ($query) {
                    $query->where('status', 'published');
                }])
                ->having('job_listings_count', '>', 0)
                ->orderBy('job_listings_count', 'desc');

            if ($limit) {
                $query->limit($limit);
            }

            return $query->get();
        });
    }

    /**
     * Get job statistics for dashboard
     */
    public function getJobStatistics()
    {
        return Cache::remember('job_statistics', 300, function () {
            return [
                'total_jobs' => JobListing::where('status', 'published')->count(),
                'total_companies' => Company::whereHas('jobListings', function ($query) {
                    $query->where('status', 'published');
                })->count(),
                'jobs_this_week' => JobListing::where('status', 'published')
                    ->where('created_at', '>=', now()->subWeek())
                    ->count(),
                'jobs_by_type' => JobListing::where('status', 'published')
                    ->select('job_type', DB::raw('count(*) as count'))
                    ->groupBy('job_type')
                    ->pluck('count', 'job_type')
                    ->toArray(),
                'jobs_by_location' => JobListing::where('status', 'published')
                    ->select('location', DB::raw('count(*) as count'))
                    ->groupBy('location')
                    ->orderBy('count', 'desc')
                    ->limit(10)
                    ->pluck('count', 'location')
                    ->toArray(),
            ];
        });
    }

    /**
     * Optimize database queries by adding missing indexes
     */
    public function optimizeQueries()
    {
        // This would typically be run as a maintenance command
        $optimizations = [];

        // Check for slow queries and suggest optimizations
        $slowQueries = $this->getSlowQueries();
        
        foreach ($slowQueries as $query) {
            $optimizations[] = $this->suggestOptimization($query);
        }

        return $optimizations;
    }

    /**
     * Clear all optimization caches
     */
    public function clearOptimizationCaches()
    {
        $cacheKeys = [
            'job_listings_*',
            'categories_with_counts_*',
            'companies_with_counts_*',
            'job_statistics',
        ];

        foreach ($cacheKeys as $pattern) {
            if (str_contains($pattern, '*')) {
                // For patterns with wildcards, we'd need to implement cache tag clearing
                // For now, just clear the specific known keys
                Cache::forget(str_replace('*', 'all', $pattern));
            } else {
                Cache::forget($pattern);
            }
        }
    }

    /**
     * Get database performance metrics
     */
    public function getPerformanceMetrics()
    {
        return [
            'cache_hit_ratio' => $this->getCacheHitRatio(),
            'average_query_time' => $this->getAverageQueryTime(),
            'slow_queries_count' => $this->getSlowQueriesCount(),
            'database_size' => $this->getDatabaseSize(),
        ];
    }

    /**
     * Private helper methods
     */
    private function getSlowQueries()
    {
        // This would query the MySQL slow query log or performance schema
        // For now, return empty array
        return [];
    }

    private function suggestOptimization($query)
    {
        // Analyze query and suggest optimizations
        return [
            'query' => $query,
            'suggestion' => 'Add appropriate indexes',
            'estimated_improvement' => '50%'
        ];
    }

    private function getCacheHitRatio()
    {
        // Calculate cache hit ratio
        return 85.5; // Placeholder
    }

    private function getAverageQueryTime()
    {
        // Get average query execution time
        return 0.025; // Placeholder (25ms)
    }

    private function getSlowQueriesCount()
    {
        // Count of slow queries
        return 0; // Placeholder
    }

    private function getDatabaseSize()
    {
        try {
            $result = DB::select("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            
            return $result[0]->size_mb ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
