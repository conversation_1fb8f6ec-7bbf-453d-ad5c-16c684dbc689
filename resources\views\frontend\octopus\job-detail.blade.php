@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">{{ $job->title }}</h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <a href="{{url('jobs')}}">Jobs</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>{{ $job->title }}</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                <div class="single-post d-flex flex-row">
                    <div class="thumb">
                        <img src="{{url('theme/web/img/post.png')}}" alt="">
                        <ul class="tags">
                            @foreach($job->categories as $category)
                                <li><a href="{{ route('category.jobs', $category->slug) }}">{{ $category->name }}</a></li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="details">
                        <div class="title d-flex flex-row justify-content-between">
                            <div class="titles">
                                <h2>{{ $job->title }}</h2>
                                <h6>{{ $job->company->name ?? 'Company' }}</h6>
                            </div>
                            <ul class="btns">
                                <li><a href="#"><span class="lnr lnr-heart"></span></a></li>
                                <li><a href="#" class="btn btn-primary">Apply Now</a></li>
                            </ul>
                        </div>
                        
                        <div class="mt-4">
                            <h4>Job Description</h4>
                            <p>{{ $job->description }}</p>
                        </div>
                        
                        @if($job->requirements)
                            <div class="mt-4">
                                <h4>Requirements</h4>
                                <p>{{ $job->requirements }}</p>
                            </div>
                        @endif
                        
                        @if($job->benefits)
                            <div class="mt-4">
                                <h4>Benefits</h4>
                                <p>{{ $job->benefits }}</p>
                            </div>
                        @endif
                        
                        <div class="mt-4">
                            <h4>Job Information</h4>
                            <h5>Job Nature: {{ ucfirst($job->job_type) }}</h5>
                            @if($job->experience_level)
                                <h5>Experience Level: {{ $job->experience_level }}</h5>
                            @endif
                            @if($job->education_level)
                                <h5>Education: {{ $job->education_level }}</h5>
                            @endif
                            <p class="address"><span class="lnr lnr-map"></span> {{ $job->location }}</p>
                            @if($job->salary_min && $job->salary_max)
                                <p class="address"><span class="lnr lnr-database"></span> ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}</p>
                            @endif
                            <p class="address"><span class="lnr lnr-calendar-full"></span> Posted {{ $job->created_at->diffForHumans() }}</p>
                            @if($job->deadline)
                                <p class="address"><span class="lnr lnr-alarm"></span> Deadline: {{ $job->deadline->format('M d, Y') }}</p>
                            @endif
                        </div>
                        
                        @if($job->company)
                            <div class="mt-4">
                                <h4>About {{ $job->company->name }}</h4>
                                @if($job->company->description)
                                    <p>{{ Str::limit($job->company->description, 300) }}</p>
                                @endif
                                <a href="{{ route('frontend.company', $job->company->slug) }}" class="btn btn-outline-primary">View Company Profile</a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4 sidebar">
                <div class="single-slidebar">
                    <h4>Quick Apply</h4>
                    <div class="mt-3">
                        <a href="#" class="btn btn-primary btn-block mb-2">Apply Now</a>
                        <a href="#" class="btn btn-outline-primary btn-block mb-2">Save Job</a>
                        <a href="{{ route('frontend.company', $job->company->slug) }}" class="btn btn-outline-primary btn-block">View Company</a>
                    </div>
                </div>
                
                @if($relatedJobs->count() > 0)
                    <div class="single-slidebar">
                        <h4>Related Jobs</h4>
                        @foreach($relatedJobs as $relatedJob)
                            <div class="job-item mb-3">
                                <a href="{{ route('frontend.job.show', $relatedJob->slug) }}">
                                    <h6>{{ $relatedJob->title }}</h6>
                                </a>
                                <p>{{ $relatedJob->company->name ?? 'Company' }}</p>
                                <p><span class="lnr lnr-map"></span> {{ $relatedJob->location }}</p>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
@endsection
