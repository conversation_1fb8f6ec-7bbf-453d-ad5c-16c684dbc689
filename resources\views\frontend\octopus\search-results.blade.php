@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">Search Results</h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>Search Results</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                @if(isset($query))
                    <div class="mb-4 text-center">
                        <h4>Search results for: "{{ $query }}"</h4>
                        <p>Found {{ $jobs->total() }} {{ Str::plural('job', $jobs->total()) }}</p>
                    </div>
                @endif
                
                @if($jobs->count() > 0)
                    @foreach($jobs as $job)
                        <div class="single-post d-flex flex-row">
                            <div class="thumb">
                                <img src="{{url('theme/web/img/post.png')}}" alt="">
                                <ul class="tags">
                                    @foreach($job->categories as $category)
                                        <li><a href="{{ route('category.jobs', $category->slug) }}">{{ $category->name }}</a></li>
                                    @endforeach
                                </ul>
                            </div>
                            <div class="details">
                                <div class="title d-flex flex-row justify-content-between">
                                    <div class="titles">
                                        <a href="{{ route('frontend.job.show', $job->slug) }}">
                                            <h4>{{ $job->title }}</h4>
                                        </a>
                                        <h6>{{ $job->company->name ?? 'Company' }}</h6>
                                    </div>
                                    <ul class="btns">
                                        <li><a href="#"><span class="lnr lnr-heart"></span></a></li>
                                        <li><a href="{{ route('frontend.job.show', $job->slug) }}">Apply</a></li>
                                    </ul>
                                </div>
                                <p>{{ Str::limit($job->description, 200) }}</p>
                                <h5>Job Nature: {{ ucfirst($job->job_type) }}</h5>
                                <p class="address"><span class="lnr lnr-map"></span> {{ $job->location }}</p>
                                @if($job->salary_min && $job->salary_max)
                                    <p class="address"><span class="lnr lnr-database"></span> ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}</p>
                                @endif
                            </div>
                        </div>
                    @endforeach
                    
                    <!-- Pagination -->
                    <div class="row justify-content-center">
                        <div class="col-lg-12">
                            {{ $jobs->appends(request()->query())->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center">
                        <h4>No jobs found</h4>
                        @if(isset($query))
                            <p>No jobs found for "{{ $query }}". Try different keywords or browse all jobs.</p>
                        @else
                            <p>No jobs found matching your criteria.</p>
                        @endif
                        <a href="{{ route('frontend.jobs') }}" class="btn btn-primary">Browse All Jobs</a>
                    </div>
                @endif
            </div>
            
            <!-- Search Sidebar -->
            <div class="col-lg-4 sidebar">
                <div class="single-slidebar">
                    <h4>Refine Search</h4>
                    <form action="{{ route('frontend.search') }}" method="GET">
                        <div class="form-group">
                            <label>Keywords</label>
                            <input type="text" name="q" class="form-control" value="{{ request('q') }}" placeholder="Job title, skills, company">
                        </div>
                        <div class="form-group">
                            <label>Location</label>
                            <input type="text" name="location" class="form-control" value="{{ request('location') }}" placeholder="City, state, country">
                        </div>
                        <div class="form-group">
                            <label>Job Type</label>
                            <select name="type" class="form-control">
                                <option value="">All Types</option>
                                <option value="full-time" {{ request('type') == 'full-time' ? 'selected' : '' }}>Full Time</option>
                                <option value="part-time" {{ request('type') == 'part-time' ? 'selected' : '' }}>Part Time</option>
                                <option value="contract" {{ request('type') == 'contract' ? 'selected' : '' }}>Contract</option>
                                <option value="freelance" {{ request('type') == 'freelance' ? 'selected' : '' }}>Freelance</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">Search Jobs</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
@endsection
