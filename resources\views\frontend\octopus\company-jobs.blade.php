@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">{{ $company->name }} Jobs</h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <a href="{{url('companies')}}">Companies</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <a href="{{ route('frontend.company', $company->slug) }}">{{ $company->name }}</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>Jobs</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                <!-- Company Info -->
                <div class="single-post d-flex flex-row mb-4">
                    <div class="details col-lg-12">
                        <div class="title text-center">
                            <h2>{{ $company->name }}</h2>
                            @if($company->description)
                                <p>{{ $company->description }}</p>
                            @endif
                            @if($company->location)
                                <p class="address"><span class="lnr lnr-map"></span> {{ $company->location }}</p>
                            @endif
                            @if($company->website)
                                <p class="address"><span class="lnr lnr-link"></span> <a href="{{ $company->website }}" target="_blank">{{ $company->website }}</a></p>
                            @endif
                        </div>
                    </div>
                </div>
                
                @if($jobs->count() > 0)
                    @foreach($jobs as $job)
                        <div class="single-post d-flex flex-row">
                            <div class="thumb">
                                <img src="{{url('theme/web/img/post.png')}}" alt="">
                                <ul class="tags">
                                    @foreach($job->categories as $category)
                                        <li><a href="{{ route('category.jobs', $category->slug) }}">{{ $category->name }}</a></li>
                                    @endforeach
                                </ul>
                            </div>
                            <div class="details">
                                <div class="title d-flex flex-row justify-content-between">
                                    <div class="titles">
                                        <a href="{{ route('frontend.job.show', $job->slug) }}">
                                            <h4>{{ $job->title }}</h4>
                                        </a>
                                        <h6>{{ $job->location }}</h6>
                                    </div>
                                    <ul class="btns">
                                        <li><a href="#"><span class="lnr lnr-heart"></span></a></li>
                                        <li><a href="{{ route('frontend.job.show', $job->slug) }}">Apply</a></li>
                                    </ul>
                                </div>
                                <p>{{ Str::limit($job->description, 200) }}</p>
                                <h5>Job Nature: {{ ucfirst($job->job_type) }}</h5>
                                <p class="address"><span class="lnr lnr-map"></span> {{ $job->location }}</p>
                                @if($job->salary_min && $job->salary_max)
                                    <p class="address"><span class="lnr lnr-database"></span> ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}</p>
                                @endif
                            </div>
                        </div>
                    @endforeach
                    
                    <!-- Pagination -->
                    <div class="row justify-content-center">
                        <div class="col-lg-12">
                            {{ $jobs->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center">
                        <h4>No jobs available at {{ $company->name }}</h4>
                        <p>Please check back later for new opportunities.</p>
                        <a href="{{ route('frontend.companies') }}" class="btn btn-primary">Browse Other Companies</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
@endsection
