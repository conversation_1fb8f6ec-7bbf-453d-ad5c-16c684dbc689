@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">
                    {{ $page->title ?? 'Page' }}
                </h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>{{ $page->title ?? 'Page' }}</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                @if(isset($page->content))
                    <div class="single-post">
                        <div class="details">
                            {!! $page->content !!}
                        </div>
                    </div>
                @endif

                @if(isset($page->data) && is_array($page->data))
                    @foreach($page->data as $key => $section)
                        @if($key === 'sections' && is_array($section))
                            @foreach($section as $sectionData)
                                <div class="single-post d-flex flex-row">
                                    <div class="details">
                                        @if(isset($sectionData['title']))
                                            <h4>{{ $sectionData['title'] }}</h4>
                                        @endif
                                        
                                        @if(isset($sectionData['items']) && is_array($sectionData['items']))
                                            <div class="row">
                                                @foreach($sectionData['items'] as $item)
                                                    <div class="col-md-6 mb-3">
                                                        <div class="thumb">
                                                            @if(isset($item['image']))
                                                                <img src="{{ $item['image'] }}" alt="{{ $item['title'] ?? '' }}">
                                                            @endif
                                                        </div>
                                                        <div class="details">
                                                            @if(isset($item['title']))
                                                                <h5>
                                                                    @if(isset($item['link']))
                                                                        <a href="{{ $item['link'] }}">{{ $item['title'] }}</a>
                                                                    @else
                                                                        {{ $item['title'] }}
                                                                    @endif
                                                                </h5>
                                                            @endif
                                                            @if(isset($item['description']))
                                                                <p>{{ $item['description'] }}</p>
                                                            @endif
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @elseif($key === 'contact_info' && is_array($section))
                            <div class="single-post d-flex flex-row">
                                <div class="details">
                                    <h4>Contact Information</h4>
                                    @if(isset($section['email']))
                                        <p><strong>Email:</strong> {{ $section['email'] }}</p>
                                    @endif
                                    @if(isset($section['phone']))
                                        <p><strong>Phone:</strong> {{ $section['phone'] }}</p>
                                    @endif
                                    @if(isset($section['address']))
                                        <p><strong>Address:</strong> {{ $section['address'] }}</p>
                                    @endif
                                </div>
                            </div>
                        @endif
                    @endforeach
                @endif
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4 sidebar">
                <div class="single-slidebar">
                    <h4>Related Links</h4>
                    <div class="blog-list">
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('jobs') }}">
                                    <h6>Browse All Jobs</h6>
                                </a>
                                <p>Find your dream job from thousands of opportunities</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('companies') }}">
                                    <h6>Top Companies</h6>
                                </a>
                                <p>Explore leading companies and their job openings</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('category') }}">
                                    <h6>Job Categories</h6>
                                </a>
                                <p>Browse jobs by industry and specialization</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
@endsection
