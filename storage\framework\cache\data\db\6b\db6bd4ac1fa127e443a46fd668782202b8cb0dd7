1753864123O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:6:{i:0;O:19:"App\Models\Category":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:1;s:4:"name";s:10:"Technology";s:4:"slug";s:10:"technology";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:11:" * original";a:5:{s:2:"id";i:1;s:4:"name";s:10:"Technology";s:4:"slug";s:10:"technology";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:9:"parent_id";i:4;s:4:"icon";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:19:"App\Models\Category":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:12;s:4:"name";s:8:"Business";s:4:"slug";s:8:"business";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:11:" * original";a:5:{s:2:"id";i:12;s:4:"name";s:8:"Business";s:4:"slug";s:8:"business";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:9:"parent_id";i:4;s:4:"icon";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:19:"App\Models\Category":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:23;s:4:"name";s:6:"Design";s:4:"slug";s:6:"design";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:11:" * original";a:5:{s:2:"id";i:23;s:4:"name";s:6:"Design";s:4:"slug";s:6:"design";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:9:"parent_id";i:4;s:4:"icon";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:19:"App\Models\Category":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:45;s:4:"name";s:9:"Education";s:4:"slug";s:9:"education";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:11:" * original";a:5:{s:2:"id";i:45;s:4:"name";s:9:"Education";s:4:"slug";s:9:"education";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:9:"parent_id";i:4;s:4:"icon";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:19:"App\Models\Category":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:56;s:4:"name";s:11:"Engineering";s:4:"slug";s:11:"engineering";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:11:" * original";a:5:{s:2:"id";i:56;s:4:"name";s:11:"Engineering";s:4:"slug";s:11:"engineering";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:9:"parent_id";i:4;s:4:"icon";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:5;O:19:"App\Models\Category":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:34;s:4:"name";s:10:"Healthcare";s:4:"slug";s:10:"healthcare";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:11:" * original";a:5:{s:2:"id";i:34;s:4:"name";s:10:"Healthcare";s:4:"slug";s:10:"healthcare";s:9:"parent_id";N;s:18:"job_listings_count";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:9:"parent_id";i:4;s:4:"icon";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}