@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">
                    {{ $page->title ?? 'Templates' }}
                </h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>{{ $page->title ?? 'Templates' }}</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start of button area-->
<div class="container">
    <div class="row justify-content-center text-center">
        <div class="col-lg-3 col-md-3 col-sm-6">
            <a href="{{url('service_templates')}}" class="btn text-uppercase btn-lg" id="businesscomp">Resumes</a><br>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6">
            <a href="{{url('cover_letter')}}" class="btn text-uppercase btn-lg btn-active" id="businesscomp">Cover Letters</a><br>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6">
            <a href="{{url('#')}}" class="btn text-uppercase btn-lg" id="businesscomp">Key Selection Criteria</a><br>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6">
            <a href="{{url('#')}}" class="btn text-uppercase btn-lg" id="businesscomp">Profile Sample</a><br>
        </div>
    </div>
</div>
<!-- End of button area-->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                @if(isset($page->content))
                    <div class="single-post">
                        <div class="details">
                            {!! $page->content !!}
                        </div>
                    </div>
                @endif

                <!-- Template Gallery -->
                <div class="single-post">
                    <div class="details">
                        <h4>Professional Templates</h4>
                        <div class="row">
                            @for($i = 1; $i <= 6; $i++)
                                <div class="col-md-4 mb-4">
                                    <div class="template-item text-center">
                                        <div class="template-preview">
                                            <img src="{{url('theme/web/img/it-resume.png')}}" alt="Template {{ $i }}" class="img-fluid">
                                            <div class="template-overlay">
                                                <button class="btn btn-primary" id="preview{{ $i }}">Preview</button>
                                                <button class="btn btn-success">Download</button>
                                            </div>
                                        </div>
                                        <h6>Professional Template {{ $i }}</h6>
                                        <p>Modern and clean design perfect for professionals</p>
                                    </div>
                                </div>
                            @endfor
                        </div>
                    </div>
                </div>

                @if(isset($page->data) && is_array($page->data))
                    @foreach($page->data as $key => $section)
                        @if($key === 'sections' && is_array($section))
                            @foreach($section as $sectionData)
                                <div class="single-post">
                                    <div class="details">
                                        @if(isset($sectionData['title']))
                                            <h4>{{ $sectionData['title'] }}</h4>
                                        @endif
                                        
                                        @if(isset($sectionData['items']) && is_array($sectionData['items']))
                                            <div class="row">
                                                @foreach($sectionData['items'] as $item)
                                                    <div class="col-md-6 mb-4">
                                                        <div class="single-post d-flex flex-row">
                                                            <div class="thumb">
                                                                @if(isset($item['image']))
                                                                    <img src="{{ $item['image'] }}" alt="{{ $item['title'] ?? '' }}">
                                                                @else
                                                                    <img src="{{url('theme/web/img/it-resume.png')}}" alt="">
                                                                @endif
                                                                @if(isset($item['category']))
                                                                    <ul class="tags">
                                                                        <li><a href="#">{{ $item['category'] }}</a></li>
                                                                    </ul>
                                                                @endif
                                                            </div>
                                                            <div class="details">
                                                                @if(isset($item['title']))
                                                                    <h6>
                                                                        @if(isset($item['link']))
                                                                            <a href="{{ $item['link'] }}">{{ $item['title'] }}</a>
                                                                        @else
                                                                            {{ $item['title'] }}
                                                                        @endif
                                                                    </h6>
                                                                @endif
                                                                @if(isset($item['description']))
                                                                    <p>{{ $item['description'] }}</p>
                                                                @endif
                                                                @if(isset($item['format']))
                                                                    <p class="address">
                                                                        <span class="lnr lnr-file-empty"></span> Format: {{ $item['format'] }}
                                                                    </p>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    @endforeach
                @endif

                <!-- Tips Section -->
                <div class="single-post">
                    <div class="details">
                        <h4>Template Tips</h4>
                        <ul>
                            <li>Choose a template that matches your industry and experience level</li>
                            <li>Customize colors and fonts to reflect your personal brand</li>
                            <li>Keep the design clean and professional</li>
                            <li>Ensure all information is accurate and up-to-date</li>
                            <li>Proofread carefully before submitting</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4 sidebar">
                <div class="single-slidebar">
                    <h4>Template Categories</h4>
                    <div class="blog-list">
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('it_resume_main') }}">
                                    <h6>IT Resume Templates</h6>
                                </a>
                                <p>Specialized templates for technology professionals</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('non_it_resume_main') }}">
                                    <h6>Non-IT Resume Templates</h6>
                                </a>
                                <p>Templates for various industries and roles</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('cover_letter_main') }}">
                                    <h6>Cover Letter Templates</h6>
                                </a>
                                <p>Professional cover letter examples</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="single-slidebar">
                    <h4>Popular Downloads</h4>
                    <div class="blog-list">
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('it_resume_web') }}">
                                    <h6>Web Developer Resume</h6>
                                </a>
                                <p>Perfect for front-end and back-end developers</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('it_resume_manager') }}">
                                    <h6>IT Manager Resume</h6>
                                </a>
                                <p>Leadership-focused template for managers</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('cover_letter_it') }}">
                                    <h6>IT Cover Letter</h6>
                                </a>
                                <p>Technical cover letter template</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->

<script>
$(document).ready(function(){
    @for($i = 1; $i <= 6; $i++)
        $("#preview{{ $i }}").click(function(){
            window.open("{{url('theme/web/img/it-resume.png')}}");
        });
    @endfor
});
</script>
@endsection
