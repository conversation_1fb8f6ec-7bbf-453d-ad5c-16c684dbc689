<?php $__env->startSection('content'); ?>
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white"><?php echo e($category->name); ?> Jobs</h1>
                <p class="text-white link-nav">
                    <a href="<?php echo e(url('')); ?>">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <a href="<?php echo e(url('jobs')); ?>">Jobs</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span><?php echo e($category->name); ?></span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                <?php if($category->description): ?>
                    <div class="mb-4 text-center">
                        <p><?php echo e($category->description); ?></p>
                    </div>
                <?php endif; ?>

                <?php if($jobs->count() > 0): ?>
                    <?php $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="single-post d-flex flex-row">
                            <div class="thumb">
                                <img src="<?php echo e(url('theme/web/img/post.png')); ?>" alt="">
                                <ul class="tags">
                                    <li><a href="<?php echo e(route('category.jobs', $category->slug)); ?>"><?php echo e($category->name); ?></a></li>
                                </ul>
                            </div>
                            <div class="details">
                                <div class="title d-flex flex-row justify-content-between">
                                    <div class="titles">
                                        <a href="<?php echo e(route('frontend.job.show', $job->slug)); ?>">
                                            <h4><?php echo e($job->title); ?></h4>
                                        </a>
                                        <h6><?php echo e($job->company->name ?? 'Company'); ?></h6>
                                    </div>
                                    <ul class="btns">
                                        <li><a href="#"><span class="lnr lnr-heart"></span></a></li>
                                        <li><a href="<?php echo e(route('frontend.job.show', $job->slug)); ?>">Apply</a></li>
                                    </ul>
                                </div>
                                <p><?php echo e(Str::limit($job->description, 200)); ?></p>
                                <h5>Job Nature: <?php echo e(ucfirst($job->job_type)); ?></h5>
                                <p class="address"><span class="lnr lnr-map"></span> <?php echo e($job->location); ?></p>
                                <?php if($job->salary_min && $job->salary_max): ?>
                                    <p class="address"><span class="lnr lnr-database"></span> $<?php echo e(number_format($job->salary_min)); ?> - $<?php echo e(number_format($job->salary_max)); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <!-- Pagination -->
                    <div class="row justify-content-center">
                        <div class="col-lg-12">
                            <?php echo e($jobs->links()); ?>

                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-center">
                        <h4>No <?php echo e($category->name); ?> jobs found</h4>
                        <p>Please check back later for new opportunities in this category.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.octopus.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\jo\joboctopus_new\resources\views/frontend/octopus/category-jobs.blade.php ENDPATH**/ ?>