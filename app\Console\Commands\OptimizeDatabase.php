<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DatabaseOptimizationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class OptimizeDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:optimize {--clear-cache : Clear optimization caches} {--analyze : Analyze database performance}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize database performance and clear caches';

    protected $dbOptimizationService;

    public function __construct(DatabaseOptimizationService $dbOptimizationService)
    {
        parent::__construct();
        $this->dbOptimizationService = $dbOptimizationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting database optimization...');

        if ($this->option('clear-cache')) {
            $this->clearCaches();
        }

        if ($this->option('analyze')) {
            $this->analyzePerformance();
        }

        if (!$this->option('clear-cache') && !$this->option('analyze')) {
            $this->runFullOptimization();
        }

        $this->info('Database optimization completed!');
    }

    protected function clearCaches()
    {
        $this->info('Clearing optimization caches...');

        $this->dbOptimizationService->clearOptimizationCaches();

        // Clear Laravel caches
        $this->call('cache:clear');
        $this->call('config:clear');
        $this->call('route:clear');
        $this->call('view:clear');

        $this->info('✓ Caches cleared successfully');
    }

    protected function analyzePerformance()
    {
        $this->info('Analyzing database performance...');

        $metrics = $this->dbOptimizationService->getPerformanceMetrics();

        $this->table(
            ['Metric', 'Value'],
            [
                ['Cache Hit Ratio', $metrics['cache_hit_ratio'] . '%'],
                ['Average Query Time', $metrics['average_query_time'] . 'ms'],
                ['Slow Queries Count', $metrics['slow_queries_count']],
                ['Database Size', $metrics['database_size'] . 'MB'],
            ]
        );

        $this->info('✓ Performance analysis completed');
    }

    protected function runFullOptimization()
    {
        $this->info('Running full database optimization...');

        // Clear caches
        $this->clearCaches();

        // Optimize MySQL tables
        $this->optimizeTables();

        // Update statistics
        $this->updateStatistics();

        // Warm up caches
        $this->warmUpCaches();

        $this->info('✓ Full optimization completed');
    }

    protected function optimizeTables()
    {
        $this->info('Optimizing database tables...');

        $tables = [
            'job_listings',
            'companies',
            'categories',
            'users',
            'job_applications',
            'job_favorites',
            'job_shortlists'
        ];

        foreach ($tables as $table) {
            try {
                DB::statement("OPTIMIZE TABLE {$table}");
                $this->line("✓ Optimized table: {$table}");
            } catch (\Exception $e) {
                $this->error("✗ Failed to optimize table {$table}: " . $e->getMessage());
            }
        }
    }

    protected function updateStatistics()
    {
        $this->info('Updating table statistics...');

        try {
            DB::statement('ANALYZE TABLE job_listings, companies, categories, users');
            $this->line('✓ Table statistics updated');
        } catch (\Exception $e) {
            $this->error('✗ Failed to update statistics: ' . $e->getMessage());
        }
    }

    protected function warmUpCaches()
    {
        $this->info('Warming up caches...');

        // Warm up common queries
        $this->dbOptimizationService->getOptimizedJobListings(10);
        $this->dbOptimizationService->getOptimizedCategories(6);
        $this->dbOptimizationService->getOptimizedCompanies(10);
        $this->dbOptimizationService->getJobStatistics();

        $this->line('✓ Caches warmed up');
    }
}
