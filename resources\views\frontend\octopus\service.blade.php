@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">
                    {{ $page->title ?? 'Services' }}
                </h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>{{ $page->title ?? 'Services' }}</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                @if(isset($page->content))
                    <div class="single-post">
                        <div class="details">
                            {!! $page->content !!}
                        </div>
                    </div>
                @endif

                @if(isset($page->data) && is_array($page->data))
                    @foreach($page->data as $key => $section)
                        @if($key === 'sections' && is_array($section))
                            @foreach($section as $sectionData)
                                <div class="single-post">
                                    <div class="details">
                                        @if(isset($sectionData['title']))
                                            <h4>{{ $sectionData['title'] }}</h4>
                                        @endif
                                        
                                        @if(isset($sectionData['items']) && is_array($sectionData['items']))
                                            <div class="row">
                                                @foreach($sectionData['items'] as $item)
                                                    <div class="col-md-6 mb-4">
                                                        <div class="single-post d-flex flex-row">
                                                            <div class="thumb">
                                                                @if(isset($item['image']))
                                                                    <img src="{{ $item['image'] }}" alt="{{ $item['title'] ?? '' }}">
                                                                @else
                                                                    <img src="{{url('theme/web/img/post.png')}}" alt="">
                                                                @endif
                                                                @if(isset($item['category']))
                                                                    <ul class="tags">
                                                                        <li><a href="#">{{ $item['category'] }}</a></li>
                                                                    </ul>
                                                                @endif
                                                            </div>
                                                            <div class="details">
                                                                @if(isset($item['title']))
                                                                    <h6>
                                                                        @if(isset($item['link']))
                                                                            <a href="{{ $item['link'] }}">{{ $item['title'] }}</a>
                                                                        @else
                                                                            {{ $item['title'] }}
                                                                        @endif
                                                                    </h6>
                                                                @endif
                                                                @if(isset($item['description']))
                                                                    <p>{{ $item['description'] }}</p>
                                                                @endif
                                                                @if(isset($item['price']))
                                                                    <p class="address">
                                                                        <span class="lnr lnr-tag"></span> {{ $item['price'] }}
                                                                    </p>
                                                                @endif
                                                                @if(isset($item['duration']))
                                                                    <p class="address">
                                                                        <span class="lnr lnr-clock"></span> {{ $item['duration'] }}
                                                                    </p>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @elseif($key === 'contact_info' && is_array($section))
                            <div class="single-post">
                                <div class="details">
                                    <h4>Contact Information</h4>
                                    @if(isset($section['email']))
                                        <p class="address">
                                            <span class="lnr lnr-envelope"></span> {{ $section['email'] }}
                                        </p>
                                    @endif
                                    @if(isset($section['phone']))
                                        <p class="address">
                                            <span class="lnr lnr-phone"></span> {{ $section['phone'] }}
                                        </p>
                                    @endif
                                    @if(isset($section['address']))
                                        <p class="address">
                                            <span class="lnr lnr-map"></span> {{ $section['address'] }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                        @endif
                    @endforeach
                @endif

                <!-- Call to Action -->
                <div class="single-post">
                    <div class="details text-center">
                        <h4>Ready to Get Started?</h4>
                        <p>Take the next step in your career journey with our professional services.</p>
                        <a href="{{ url('contact') }}" class="primary-btn">Contact Us</a>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4 sidebar">
                <div class="single-slidebar">
                    <h4>Our Services</h4>
                    <div class="blog-list">
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('service_templates') }}">
                                    <h6>Resume Templates</h6>
                                </a>
                                <p>Professional resume templates for all industries</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('cover_letter') }}">
                                    <h6>Cover Letters</h6>
                                </a>
                                <p>Compelling cover letter examples and templates</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('training') }}">
                                    <h6>Training Programs</h6>
                                </a>
                                <p>Skill development and certification courses</p>
                            </div>
                        </div>
                        <div class="single-blog d-flex flex-row">
                            <div class="details">
                                <a href="{{ url('counselling') }}">
                                    <h6>Career Counselling</h6>
                                </a>
                                <p>Expert guidance for your career decisions</p>
                            </div>
                        </div>
                    </div>
                </div>

                @if(isset($page->data['navigation']) && is_array($page->data['navigation']))
                    <div class="single-slidebar">
                        <h4>Quick Links</h4>
                        <div class="blog-list">
                            @foreach($page->data['navigation'] as $nav)
                                <div class="single-blog d-flex flex-row">
                                    <div class="details">
                                        <a href="{{ $nav['link'] }}">
                                            <h6>{{ $nav['title'] }}</h6>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
@endsection
